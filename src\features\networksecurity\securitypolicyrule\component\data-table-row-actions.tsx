
import { DotsHorizontalIcon } from '@radix-ui/react-icons'
import { IconEdit, IconTrash } from '@tabler/icons-react'
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from '@/components/ui/dialog'
import axios_api from '@/lib/axios_api'
import { toast } from 'sonner'
import NetworkYamlEditor from '../../../networksecurity/networkyaml'


interface RowOriginal {
  name?: string;
  namespace?: string;
  labels?: Record<string, string>;
  annotation?: Record<string, string>;
  sessionId?: string;
  podSelector?: any;
  policyTypes?: string[];
}

interface DataTableRowActionsProps<TData extends { original: RowOriginal }> {
  row: TData;
  onRefresh: () => Promise<any>;
}

export function DataTableRowActions<TData extends { original: RowOriginal }>({
  row,
  onRefresh
}: DataTableRowActionsProps<TData>) {
  const [loading, setLoading] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showYamlEditor, setShowYamlEditor] = useState(false)
  const [yamlContent, setYamlContent] = useState("")


  const handleDeleteNetworkPolicy = async () => {
    try {
      const name = row.original.name
      const namespace = row.original.namespace
      
      if (!name || !namespace) {
        toast.error("Missing name or namespace")
        return
      }
      
      setLoading(true)
      await axios_api.delete(`/k8s/networkpolicies/delete?name=${name}&namespace=${namespace}`)

      toast.success("Network Policy deleted successfully!")
      setShowDeleteDialog(false)
      
      // Refresh the network policies list after deletion
      await onRefresh()
    } catch (error) {
    } finally {
      setLoading(false)
    }
  }

  const handleEditNetworkPolicy = async () => {
    try {
      const name = row.original.name
      const namespace = row.original.namespace
      
      if (!name || !namespace) {
        toast.error("Missing name or namespace")
        return
      }
      
      setLoading(true)
      const response = await axios_api.get(`/k8s/yaml/get?objectType=networkpolicy&namespace=${namespace}&objectName=${name}`)
      const receivedYamlData = response.data

      setYamlContent(receivedYamlData)
      setShowYamlEditor(true)
    } catch (error) {
    } finally {
      setLoading(false)
    }
  }

  return (
    <>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button
            variant='ghost'
            className='data-[state=open]:bg-muted flex h-8 w-8 p-0'
          >
            <DotsHorizontalIcon className='h-4 w-4' />
            <span className='sr-only'>Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end' className='w-[160px]'>
          <DropdownMenuItem onClick={handleEditNetworkPolicy}>
            Edit YAML
            <DropdownMenuShortcut>
              <IconEdit size={16} />
            </DropdownMenuShortcut>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem 
            onClick={() => setShowDeleteDialog(true)}
            className="text-destructive focus:text-destructive"
          >
            Delete
            <DropdownMenuShortcut>
              <IconTrash size={16} />
            </DropdownMenuShortcut>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>


      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete Network Policy</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the network policy "{row.original.name}" in namespace "{row.original.namespace}"?
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)} disabled={loading}>Cancel</Button>
            <Button variant="destructive" onClick={handleDeleteNetworkPolicy} disabled={loading}>
              {loading ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* YAML Editor Dialog */}
      {showYamlEditor && (
        <NetworkYamlEditor
          open={showYamlEditor}
          yamlContent={yamlContent}
          onClose={() => {
            setShowYamlEditor(false);
            onRefresh(); // Refresh after closing in case changes were made
          }}
        />
      )}
    </>
  )
}
