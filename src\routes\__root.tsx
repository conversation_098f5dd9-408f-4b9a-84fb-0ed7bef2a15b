import { QueryClient } from '@tanstack/react-query'
import { createRootRouteWithContext, Outlet, useNavigate } from '@tanstack/react-router'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { TanStackRouterDevtools } from '@tanstack/router-devtools'
import { Toaster } from '@/components/ui/sonner'
import { NavigationProgress } from '@/components/navigation-progress'
import GeneralError from '@/features/errors/general-error'
import NotFoundError from '@/features/errors/not-found-error'
import { useAuthStore } from '@/stores/authStore'
import { useEffect } from 'react'

export const Route = createRootRouteWithContext<{
  queryClient: QueryClient
}>()({
  component: () => {
    const navigate = useNavigate()
    const { accessToken } = useAuthStore(state => state.auth);
    
    useEffect(() => {
      // Geçerli URL'yi kontrol et
      const currentPath = window.location.pathname;
      const isAuthRoute = currentPath.includes('/sign-in') || 
                          currentPath.includes('/sign-up') || 
                          currentPath.includes('/forgot-password');
      
      // Oturum durumunu kontrol et ve gerekirse yönlendir
      if (!accessToken && !isAuthRoute) {
        // Oturum yoksa ve auth sayfalarında değilse giriş sayfasına yönlendir
        navigate({ to: '/sign-in-2', replace: true });
      } else if (accessToken && isAuthRoute) {
        // Oturum varsa ve auth sayfalarındaysa ana sayfaya yönlendir
        navigate({ to: '/', replace: true });
      }
      
      // Tarayıcı geçmişi olayını dinle
      const handlePopState = () => {
        // Geçmişte gezinirken oturum durumunu tekrar kontrol et
        const newPath = window.location.pathname;
        const isNewPathAuth = newPath.includes('/sign-in') || 
                             newPath.includes('/sign-up') || 
                             newPath.includes('/forgot-password');
        
        if (!accessToken && !isNewPathAuth) {
          navigate({ to: '/sign-in-2', replace: true });
        }
      };
      
      // Tarayıcı geçmişi olayını dinlemeye başla
      window.addEventListener('popstate', handlePopState);
      
      // Component unmount olduğunda event listener'ı temizle
      return () => {
        window.removeEventListener('popstate', handlePopState);
      };
    }, [accessToken, navigate]);

    return (
      <>
        <NavigationProgress />
        <Outlet />
        <Toaster duration={3000} />
        {import.meta.env.MODE === 'development' && (
          <>
            <ReactQueryDevtools buttonPosition='bottom-left' />
            <TanStackRouterDevtools position='bottom-right' />
          </>
        )}
      </>
    )
  },
  notFoundComponent: NotFoundError,
  errorComponent: GeneralError,
})
