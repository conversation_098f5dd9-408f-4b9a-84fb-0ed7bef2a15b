import { z } from 'zod'

// Define the role schema
export const roleSchema = z.object({
  id: z.number(),
  roleName: z.string()
})

export type Role = z.infer<typeof roleSchema>
export const roleListSchema = z.array(roleSchema)

// Define the command allowlist item schema
export const commandAllowlistItemSchema = z.object({
  id: z.number(),
  roleName: z.string(),
  commandName: z.string(),
  description: z.string().nullable(),
  createdAt: z.string(),
  updatedAt: z.string()
})

export type CommandAllowlistItem = z.infer<typeof commandAllowlistItemSchema>
export const commandAllowlistItemsSchema = z.array(commandAllowlistItemSchema)