import React, { createContext, useCallback, useContext, useState } from 'react'

import { Role, roleListSchema, CommandAllowlistItem, commandAllowlistItemsSchema } from '../data/schema'
import { toast } from 'sonner'
import axios_api from '@/lib/axios_api'

type RolesDialogType = 'add' | 'edit' | 'delete' | null

interface RolesContextType {
  roles: Role[]
  commandItems: CommandAllowlistItem[]
  currentRole: Role | null
  open: RolesDialogType
  activeTab: string
  setOpen: (type: RolesDialogType) => void
  setCurrentRole: (role: Role | null) => void
  setActiveTab: (tab: string) => void
  fetchRoles: () => Promise<void>
  fetchCommandItems: (roleName: string) => Promise<void>
  addCommandItem: (item: CommandAllowlistItem) => Promise<void>
  deleteCommandItem: (item: CommandAllowlistItem) => Promise<void>
}

const RolesContext = createContext<RolesContextType | null>(null)

export function useRoles() {
  const context = useContext(RolesContext)
  if (!context) {
    throw new Error('useRoles must be used within a RolesProvider')
  }
  return context
}

interface Props {
  children: React.ReactNode
}

export default function RolesProvider({ children }: Props) {
  const [open, setOpen] = useState<RolesDialogType>(null)
  const [roles, setRoles] = useState<Role[]>([])
  const [commandItems, setCommandItems] = useState<CommandAllowlistItem[]>([])
  const [currentRole, setCurrentRole] = useState<Role | null>(null)

  const [activeTab, setActiveTab] = useState<string>('roles')

  const fetchRoles = useCallback(async () => {
    try {
      const response = await axios_api.get('/k8s/allroles')
      const roleList = roleListSchema.parse(response.data)
      setRoles(roleList)
    } catch (err) {
      toast.error('Failed to fetch roles')
      console.error(err)
    }
  }, [])

  const fetchCommandItems = useCallback(async (roleName: string) => {
    try {
      const response = await axios_api.get(`/k8s/role-commands/${roleName}/get`)
      const items = commandAllowlistItemsSchema.parse(response.data)
      setCommandItems(items)
    } catch (err) {
      toast.error('Failed to fetch command items')
      console.error(err)
    }
  }, [])

  const addCommandItem = async (item: CommandAllowlistItem) => {
    try {
      await axios_api.post(`/k8s/role-commands/${item.roleName}/add`, item)
      await fetchCommandItems(item.roleName)
      toast.success('Command added successfully')
      return Promise.resolve()
    } catch (error) {
      toast.error('Failed to add command')
      return Promise.reject(error)
    }
  }



  const deleteCommandItem = async (item: CommandAllowlistItem) => {
    try {
      // Assuming the delete endpoint follows REST pattern
      await axios_api.delete(`/k8s/role-commands/${item.roleName}/delete/${item.id}`)
      await fetchCommandItems(item.roleName)
      toast.success('Command deleted successfully')
      return Promise.resolve()
    } catch (error) {
      toast.error('Failed to delete command')
      return Promise.reject(error)
    }
  }

  return (
    <RolesContext.Provider
      value={{
        roles,
        commandItems,
        currentRole,
        open,
        activeTab,
        setOpen,
        setCurrentRole,
        setActiveTab,
        fetchRoles,
        fetchCommandItems,
        addCommandItem,
        deleteCommandItem
      }}
    >
      {children}
    </RolesContext.Provider>
  )
}