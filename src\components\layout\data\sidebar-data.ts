import {
  IconHelp,
  IconLayoutDashboard,
  IconMessages,
  IconNotification,
  IconPalette,
  IconSettings,
  IconCloud,
  IconUserCog,
  IconSpaces,
  IconSettingsCheck,
  IconLockPassword,
  IconDeviceFloppy,
  IconDatabaseExclamation,
  IconAffiliate,
  IconNetwork,
  IconCalendarCheck,
  IconCircleDottedLetterD,
  IconBrandDocker,
  IconApiApp,
  IconServer,
  IconIkosaedr,
  IconShare,
  IconShield,
  IconDatabaseExport,
  IconUsersGroup,
} from '@tabler/icons-react'
import { AudioWaveform, GalleryVerticalEnd } from 'lucide-react'
import { LogoComponentTransparentBg } from '../logo-component'
import { type SidebarData } from '../types'

export const sidebarData: SidebarData = {
  user: {
    name: 'satnaing',
    email: '<EMAIL>',
    avatar: '/avatars/shadcn.jpg',
  },
  teams: [
    {
      name: 'Kubepakt',
      logo: LogoComponentTransparentBg,
      plan: 'Admin Dashboard',
    },
    {
      name: 'Acme Inc',
      logo: GalleryVerticalEnd,
      plan: 'Enterprise',
    },
    {
      name: 'Acme Corp.',
      logo: AudioWaveform,
      plan: 'Startup',
    },
  ],
  navGroups: [
    {
      title: 'General',
      items: [
        {
          title: 'Dashboard',
          url: '/',
          icon: IconLayoutDashboard,
        },
        {
          title: 'AI Assistant',
          url: '/chats',
          badge: 'k8s',
          icon: IconMessages,
        },
        {
          title: 'K8S Graph',
          url: '/k8sgraph/k8sgraph',
          icon: IconShare,
        },

      ],
    },
    {
      title: 'Kubernetes',
      items: [
        {
          title: 'Overview',
          icon: IconCloud,
          items: [
            {
              title: 'Overview',
              url: '/k8s',
              icon: IconIkosaedr,
            },
          ],
        },
        {
          title: 'Cluster Resources',
          icon: IconServer,
          items: [
            {
              title: 'Nodes',
              url: '/k8s/nodes',
              icon: IconServer,
            },
            {
              title: 'Namespaces',
              url: '/k8s/namespaces',
              icon: IconSpaces,
            },
          ],
        },
        {
          title: 'Workload Resources',
          icon: IconApiApp,
          items: [
            {
              title: 'Deployments',
              url: '/k8s/deployments',
              icon: IconApiApp,
            },
            {
              title: 'Pods',
              url: '/k8s/pods',
              icon: IconBrandDocker,
            },
            {
              title: 'Daemonsets',
              url: '/k8s/daemonsets',
              icon: IconCircleDottedLetterD,
            },
            {
              title: 'CronJobs',
              url: '/k8s/cronjobs',
              icon: IconCalendarCheck,
            },
          ],
        },
        {
          title: 'Network Resources',
          icon: IconNetwork,
          items: [
            {
              title: 'Services',
              url: '/k8s/services',
              icon: IconNetwork,
            },
            {
              title: 'Ingresses',
              url: '/k8s/ingresses',
              icon: IconAffiliate,
            },
          ],
        },
        {
          title: 'Storage Resources',
          icon: IconDeviceFloppy,
          items: [
            {
              title: 'Volumes',
              url: '/k8s/volumes',
              icon: IconDeviceFloppy,
            },
            {
              title: 'Volume Claims',
              url: '/k8s/volumes_claims',
              icon: IconDatabaseExclamation,
            },
          ],
        },
        {
          title: 'Configuration',
          icon: IconSettings,
          items: [
            {
              title: 'ConfigMaps',
              url: '/k8s/configmaps',
              icon: IconSettingsCheck,
            },
            {
              title: 'Secrets',
              url: '/k8s/secrets',
              icon: IconLockPassword,
            },
          ],
        },
      ],
    },
    {
      title: 'Security',
      items: [
        {
          title: 'Vulnerability',
          url: '/vulnerability',
          icon: IconIkosaedr,
        },
        {
          title: 'Network Security',
          url: '/networksecurity',
          icon: IconShield,
        },
      ],
    },
     {
      title: 'Backup',
      items: [
        {
          title: 'Explorer',
          url: '/backup',
          icon: IconDatabaseExport,
        },
      ],
    },
    {
      title: 'Administration',
      items: [
        {
          title: 'Users',
          url: '/users',
          icon: IconUsersGroup,
        },
        {
          title: 'Roles',
          url: '/roles',
          icon: IconShield,
        },
        // Other admin items...
      ],
    },
    {
      title: 'Other',
      items: [
        {
          title: 'Settings',
          icon: IconSettings,
          items: [
            {
              title: 'Profile',
              url: '/settings',
              icon: IconUserCog,
            },

            {
              title: 'Appearance',
              url: '/settings/appearance',
              icon: IconPalette,
            },
            {
              title: 'Notifications',
              url: '/settings/notifications',
              icon: IconNotification,
            },
          ],
        },
        {
          title: 'Help Center',
          url: '/help-center',
          icon: IconHelp,
        },
      ],
    },    
  ],
}
