import { Checkbox } from '@/components/ui/checkbox'
import { ColumnDef } from '@tanstack/react-table';
import { DataTableRowActions } from './data-table-row-actions';
import { DataTableColumnHeader } from '../../components/data-table-column-header';
import { formatDateToLocal } from '@/lib/dateformat';



interface DataEntity {
  id?: string;
  name?: string;
  namespace?: string;
  createdAt?: string;
  labels: Record<string, string>;
  annotation: Record<string, string>;
  lastSeenAt?: string;
}


export const columns: ColumnDef<DataEntity>[] = [
   {
    id: 'select',
    header: () => null, // Remove header checkbox for multiple selection
    cell: ({ row, table }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => {
          // Deselect all rows first
          table.toggleAllRowsSelected(false)
          // Then select only this row
          row.toggleSelected(!!value)
        }}
        aria-label='Select row'
        className='translate-y-[2px]'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Name' />,
    cell: ({ row }) => <div className='w-[100px]'>{row.getValue('name')}</div>,
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'namespace',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Namespace' />,
    cell: ({ row }) => <div className='w-[100px]'>{row.getValue('namespace')}</div>,
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Created At' />,
    cell: ({ row }) => <div >{formatDateToLocal(row.getValue('createdAt'))}</div>,
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'lastSeenAt',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Last Seen At' />,
    cell: ({ row }) => <div>{formatDateToLocal(row.getValue('lastSeenAt'))}</div>,
    enableSorting: false,
    enableHiding: false,
  },
  {
    id: 'actions',
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
];




