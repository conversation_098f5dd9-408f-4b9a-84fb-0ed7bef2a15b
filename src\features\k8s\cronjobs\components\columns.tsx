import { Checkbox } from '@/components/ui/checkbox'
import { ColumnDef } from '@tanstack/react-table';
import { DataTableRowActions } from './data-table-row-actions';
import { DataTableColumnHeader } from '../../components/data-table-column-header';
import { formatDateToLocal } from '@/lib/dateformat';



interface DataEntity {
  id: string;
  name: string;
  namespace: string;
  image: string;
  applicationType: string;
  status: string;
  published: string;
  labels?: Record<string, string>;
  annotation?: Record<string, string>;
  lastSeenAt: string;
}


export const columns: ColumnDef<DataEntity>[] = [
   {
    id: 'select',
    header: () => null, // Remove header checkbox for multiple selection
    cell: ({ row, table }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => {
          // Deselect all rows first
          table.toggleAllRowsSelected(false)
          // Then select only this row
          row.toggleSelected(!!value)
        }}
        aria-label='Select row'
        className='translate-y-[2px]'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Name' />
    ),
    cell: ({ row }) => <div >{row.getValue('name')}</div>,
    enableSorting: true,
    enableHiding: false,
  }, {
    accessorKey: 'namespace',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Namespace' />
    ),
    cell: ({ row }) => <div >{row.getValue('namespace')}</div>,
    enableSorting: true,
    enableHiding: false,
  }, {
    accessorKey: 'image',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Image' />
    ),
    cell: ({ row }) => <div >{row.getValue('image')}</div>,
    enableSorting: true,
    enableHiding: false,
  }, {
    accessorKey: 'applicationType',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='ApplicationType' />
    ),
    cell: ({ row }) => <div >{row.getValue('applicationType')}</div>,
    enableSorting: true,
    enableHiding: false,
  }, {
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='LastScheduleTime' />
    ),
    cell: ({ row }) => {
      const status = row.getValue('status');
      let lastScheduleTime = '';

      try {
        const statusObj = typeof status === 'string' ? JSON.parse(status) : status;
        lastScheduleTime = statusObj.lastScheduleTime || '';
      } catch {
        lastScheduleTime = '';
      }

      return <div>{formatDateToLocal(lastScheduleTime)}</div>;
    },
    enableSorting: true,
    enableHiding: false,
  }, {
    accessorKey: 'published',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Published' />
    ),
    cell: ({ row }) => <div >{row.getValue('published')}</div>,
    enableSorting: true,
    enableHiding: false,
  }, {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='CreatedAt' />
    ),
    cell: ({ row }) => <div >{formatDateToLocal(row.getValue('createdAt'))}</div>,
    enableSorting: true,
    enableHiding: false,
  }, {
    accessorKey: 'lastSeenAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='LastSeenAt' />
    ),
    cell: ({ row }) => <div>{formatDateToLocal(row.getValue('lastSeenAt'))}</div>,
    enableSorting: true,
    enableHiding: false,
  },
  {
    id: 'actions',
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
]



