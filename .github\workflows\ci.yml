name: Build and Deploy to Kubernetes

on:
  push:
    branches:
      - main

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    environment: production

    steps:
    - name: Checkout main repo
      uses: actions/checkout@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Log in to GitHub Container Registry
      uses: docker/login-action@v2
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.TOKEN }}

    - name: Build Docker image
      run: |
        docker build --build-arg GITHUB_TOKEN=${{ secrets.TOKEN }} \
          -t ghcr.io/${{ github.repository_owner }}/frontend:latest .

    - name: Push Docker image to GHCR
      run: |
        docker push ghcr.io/${{ github.repository_owner }}/frontend:latest

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ secrets.AWS_REGION }}

    - name: Get EC2 Master Publixc IP
      id: ec2ip
      run: |
        IP=$(aws ec2 describe-instances \
          --filters "Name=tag:Name,Values=k8s-master" \
          --query "Reservations[].Instances[].PublicIpAddress" \
          --output text --region ${{ secrets.AWS_REGION }})
        echo "EC2_IP=$IP" >> $GITHUB_OUTPUT

    - name: Checkout kubepakt-infra repo (deployment yamlları ve ssh key burada)
      uses: actions/checkout@v3
      with:
        repository: datapakt/kubepakt-infra
        token: ${{ secrets.TOKEN }}
        path: infra

    - name: SSH to EC2 and deploy Kubernetes manifests
      run: |
        chmod 600 infra/kubernetes_on_ec2/k8s
        EC2_IP=${{ steps.ec2ip.outputs.EC2_IP }}

        echo ">>> Copying manifest to $EC2_IP..."
        scp -i infra/kubernetes_on_ec2/k8s -o StrictHostKeyChecking=no \
          infra/k8s/frontend-bundle.yaml ubuntu@$EC2_IP:/tmp/

        echo ">>> Applying manifests with kubectl..."
        ssh -i infra/kubernetes_on_ec2/k8s -o StrictHostKeyChecking=no \
          ubuntu@$EC2_IP "kubectl apply -f /tmp/frontend-bundle.yaml"

        echo ">>> Forcing rollout restart to ensure image refresh..."
        ssh -i infra/kubernetes_on_ec2/k8s -o StrictHostKeyChecking=no \
          ubuntu@$EC2_IP "kubectl rollout restart deployment frontend"

