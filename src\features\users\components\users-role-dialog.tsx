'use client'

import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { IconUserShield } from '@tabler/icons-react'
import { showSubmittedData } from '@/utils/show-submitted-data'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Checkbox } from '@/components/ui/checkbox'
import { useUsers } from '../context/users-context'
import { User } from '../data/schema'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow: User
}

export function UsersRoleDialog({ open, onOpenChange, currentRow }: Props) {
  const { roles, assignRoles } = useUsers()
  
  const formSchema = z.object({
    selectedRoles: z.array(z.string()),
  })

  type RoleForm = z.infer<typeof formSchema>

  const form = useForm<RoleForm>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      selectedRoles: currentRow?.roles || [],
    },
  })

  const onSubmit = async (values: RoleForm) => {
    try {
      // Make sure we're sending the complete list of roles the user should have
      await assignRoles(currentRow.username, values.selectedRoles)
      
      showSubmittedData({
        username: currentRow.username,
        roles: values.selectedRoles,
      }, 'Roles assigned successfully:')
      
      onOpenChange(false)
      form.reset()
    } catch (error) {
      console.error('Error assigning roles:', error)
    }
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(state) => {
        form.reset()
        onOpenChange(state)
      }}
    >
      <DialogContent className='sm:max-w-md'>
        <DialogHeader className='text-left'>
          <DialogTitle className='flex items-center gap-2'>
            <IconUserShield /> Assign Roles
          </DialogTitle>
          <DialogDescription>
            Assign roles to {currentRow?.username}. These roles determine the user's permissions in the system.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            id='user-roles-form'
            onSubmit={form.handleSubmit(onSubmit)}
            className='space-y-4'
          >
            <FormField
              control={form.control}
              name='selectedRoles'
              render={() => (
                <FormItem>
                  <div className='mb-4'>
                    <FormLabel className='text-base'>Available Roles</FormLabel>
                  </div>
                  {roles.map((role) => (
                    <FormField
                      key={role}
                      control={form.control}
                      name='selectedRoles'
                      render={({ field }) => {
                        return (
                          <FormItem
                            key={role}
                            className='flex flex-row items-start space-x-3 space-y-0'
                          >
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes(role)}
                                onCheckedChange={(checked) => {
                                  return checked
                                    ? field.onChange([...field.value, role])
                                    : field.onChange(
                                        field.value?.filter(
                                          (value) => value !== role
                                        )
                                      )
                                }}
                              />
                            </FormControl>
                            <FormLabel className='font-normal'>
                              {role}
                            </FormLabel>
                          </FormItem>
                        )
                      }}
                    />
                  ))}
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
        <DialogFooter>
          <Button type='submit' form='user-roles-form'>
            Save changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

