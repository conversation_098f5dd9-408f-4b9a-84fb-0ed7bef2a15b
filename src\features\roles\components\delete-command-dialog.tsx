'use client'

import { Icon<PERSON>lertTriangle } from '@tabler/icons-react'
import { ConfirmDialog } from '@/components/confirm-dialog'
import { CommandAllowlistItem } from '../data/schema'
import { useRoles } from '../context/roles-context'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  commandItem: CommandAllowlistItem | null
}

export default function DeleteCommandDialog({ open, onOpenChange, commandItem }: Props) {
  const { deleteCommandItem } = useRoles()

  const handleDelete = async () => {
    if (!commandItem) return

    try {
      await deleteCommandItem(commandItem)
      onOpenChange(false)
    } catch (error) {
      // Error is already handled in the context
      console.error('Error deleting command:', error)
    }
  }

  if (!commandItem) return null

  return (
    <ConfirmDialog
      open={open}
      onOpenChange={onOpenChange}
      handleConfirm={handleDelete}
      title={
        <span className='text-destructive'>
          <IconAlertTriangle
            className='stroke-destructive mr-1 inline-block'
            size={18}
          />{' '}
          Delete Command
        </span>
      }
      desc={
        <div className='space-y-2'>
          <p>
            Are you sure you want to delete the command{' '}
            <span className='font-bold'>"{commandItem.commandName}"</span>{' '}
            from role{' '}
            <span className='font-bold'>{commandItem.roleName}</span>?
          </p>
          <p className='text-sm text-muted-foreground'>
            This action cannot be undone.
          </p>
        </div>
      }
      confirmText='Delete'
      destructive
    />
  )
}
