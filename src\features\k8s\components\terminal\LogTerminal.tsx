import {useEffect, useRef, useState} from "react";
import {Terminal} from "xterm";
import {FitAddon} from "xterm-addon-fit";
import "xterm/css/xterm.css";
import { useAuthStore } from "@/stores/authStore";

export interface KubernetesTerminalProps {
    podName: string;
    namespace: string;
    isOpen?: boolean;
}

const LogTerminal = ({namespace, podName, isOpen = true}: KubernetesTerminalProps) => {
    const term = useRef<Terminal|null>(null);
    const logSocket = useRef<WebSocket|null>(null);
    const [logs, setLogs] = useState("");
    
    // 📌 Pod loglarını WebSocket ile dinle
    useEffect(() => {
        term.current = new Terminal({
            cursorBlink: true,
            fontSize: 14,
            theme: {background: "#000000", foreground: "#ffffff"},
        });

        const fitAddon = new FitAddon();
        term.current.loadAddon(fitAddon);
        fitAddon.fit();

        const token = useAuthStore.getState().auth.accessToken || '';
        const protocol = window.location.protocol === 'https:' ? 'wss' : 'ws';
        logSocket.current = new WebSocket(`${protocol}://${window.location.host}/ws/logs?token=${token}&podName=${podName}&namespace=${namespace}`);

        logSocket.current.onopen = () => {
            term.current?.writeln("✅ Connection successful... Welcome to Kubernetes Pod!\n");
        };

        logSocket.current.onmessage = (event) => {
            setLogs((prevLogs) => prevLogs + event.data + "\n");
        };

        logSocket.current.onerror = (error) => console.error("WebSocket Error:", error);

        logSocket.current.onclose = () => term.current!.writeln("🔴 Connection closed.");
        
        return () => {
            if (logSocket.current) {
                console.log("Closing log WebSocket connection on unmount");
                logSocket.current.close();
                logSocket.current = null;
            }
        };
    }, [namespace, podName]);

    // Close socket when dialog closes
    useEffect(() => {
        if (!isOpen && logSocket.current) {
            console.log("Closing log WebSocket connection on dialog close");
            logSocket.current.close();
            logSocket.current = null;
        }
    }, [isOpen]);

    return (
        <div className="h-screen overflow-auto bg-black p-2 text-green-400 text-sm">
            <pre style={{width: "100%", height: "100vh", backgroundColor: "#000"}}>
            {logs}
            </pre>
        </div>
    );
};

export default LogTerminal;
