import { useState, useEffect } from 'react'
import { IconPlanet, IconCheck, IconX, IconBrandUber, IconBook, IconCode } from '@tabler/icons-react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { toast } from 'sonner'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import jsYaml from 'js-yaml'

export default function HelpCenter() {
  const [content, setContent] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [isValid, setIsValid] = useState<boolean | null>(null)
  const [activeTab, setActiveTab] = useState('yaml')

  // Clear error when content changes
  useEffect(() => {
    if (error) {
      setError(null)
      setIsValid(null)
    }
  }, [content, activeTab])

  const verifyContent = () => {
    try {
      if (!content.trim()) {
        throw new Error(`${activeTab.toUpperCase()} content is empty`)
      }

      switch (activeTab) {
        case 'yaml':
          // Parse YAML using js-yaml
          jsYaml.load(content)
          break
        case 'json':
          // Parse JSON
          JSON.parse(content)
          break
        case 'xml':
          // Basic XML validation
          if (!content.includes('<') || !content.includes('>')) {
            throw new Error('Invalid XML: Missing tags')
          }
          
          // Use DOMParser for XML validation
          const parser = new DOMParser()
          const xmlDoc = parser.parseFromString(content, 'text/xml')
          
          // Check for parsing errors
          const parserError = xmlDoc.querySelector('parsererror')
          if (parserError) {
            throw new Error(parserError.textContent || 'Invalid XML format')
          }
          break
      }
      
      setError(null)
      setIsValid(true)
      toast.success(`${activeTab.toUpperCase()} is valid!`)
    } catch (err) {
      setError(err instanceof Error ? err.message : `Invalid ${activeTab.toUpperCase()} format`)
      setIsValid(false)
      toast.error(`${activeTab.toUpperCase()} validation failed`)
    }
  }

  return (
    <div className='min-h-svh p-6'>
      <div className='max-w-6xl mx-auto'>
        <div className='flex flex-col items-center mb-8'>
          <IconPlanet size={72} />
          <h1 className='text-4xl leading-tight font-bold mt-4'>Developer Tools</h1>
          <p className='text-muted-foreground text-center mt-2'>
            Helpful tools and resources for developers
          </p>
        </div>
        
        <div className='grid grid-cols-1 md:grid-cols-2 gap-8'>
          {/* Validator Section */}
          <Card className='col-span-1'>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <IconCode size={20} />
                Code Validator
              </CardTitle>
              <CardDescription>
                Validate YAML, JSON, or XML syntax
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className='mb-4'>
                  <TabsTrigger value='yaml'>YAML</TabsTrigger>
                  <TabsTrigger value='json'>JSON</TabsTrigger>
                  <TabsTrigger value='xml'>XML</TabsTrigger>
                </TabsList>
                
                <TabsContent value='yaml' className='mt-0'>
                  <Textarea 
                    className='min-h-[200px] font-mono'
                    placeholder="Paste your YAML here..."
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                  />
                </TabsContent>
                
                <TabsContent value='json' className='mt-0'>
                  <Textarea 
                    className='min-h-[200px] font-mono'
                    placeholder="Paste your JSON here..."
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                  />
                </TabsContent>
                
                <TabsContent value='xml' className='mt-0'>
                  <Textarea 
                    className='min-h-[200px] font-mono'
                    placeholder="Paste your XML here..."
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                  />
                </TabsContent>
              </Tabs>
              
              {error && (
                <div className='mt-2 text-destructive text-sm flex items-start gap-1'>
                  <IconX className="mt-0.5 flex-shrink-0" size={16} />
                  <span>{error}</span>
                </div>
              )}
              
              {isValid === true && (
                <div className='mt-2 text-green-500 text-sm flex items-center gap-1'>
                  <IconCheck size={16} />
                  <span>{activeTab.toUpperCase()} syntax is valid</span>
                </div>
              )}
              
              <Button 
                className='mt-4 w-full'
                onClick={verifyContent}
                disabled={!content.trim()}
              >
                Verify {activeTab.toUpperCase()}
              </Button>
            </CardContent>
          </Card>
          
          {/* Documentation Section */}
          <Card className='col-span-1'>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <IconBook size={20} />
                Documentation
              </CardTitle>
              <CardDescription>
                Quick access to helpful documentation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <div className='border rounded-lg p-4'>
                  <h3 className='text-lg font-medium flex items-center gap-2 mb-2'>
                    <IconBrandUber size={20} />
                    Kubernetes Documentation
                  </h3>
                  <p className='text-sm text-muted-foreground mb-3'>
                    Official resources for Kubernetes configuration and management
                  </p>
                  <div className='grid grid-cols-1 sm:grid-cols-2 gap-2'>
                    <Button variant="outline" className='justify-start' onClick={() => window.open('https://kubernetes.io/docs/home/', '_blank')}>
                      Official Docs
                    </Button>
                    <Button variant="outline" className='justify-start' onClick={() => window.open('https://kubernetes.io/docs/concepts/', '_blank')}>
                      Concepts
                    </Button>
                    <Button variant="outline" className='justify-start' onClick={() => window.open('https://kubernetes.io/docs/tasks/', '_blank')}>
                      Tasks
                    </Button>
                    <Button variant="outline" className='justify-start' onClick={() => window.open('https://kubernetes.io/docs/reference/', '_blank')}>
                      API Reference
                    </Button>
                  </div>
                </div>
                
                <div className='border rounded-lg p-4'>
                  <h3 className='text-lg font-medium flex items-center gap-2 mb-2'>
                    <IconBook size={20} />
                    System Documentation
                  </h3>
                  <p className='text-sm text-muted-foreground mb-3'>
                    Resources for using and configuring this application
                  </p>
                  <div className='grid grid-cols-1 sm:grid-cols-2 gap-2'>
                    <Button variant="outline" className='justify-start' onClick={() => window.open('/docs/getting-started', '_self')}>
                      Getting Started
                    </Button>
                    <Button variant="outline" className='justify-start' onClick={() => window.open('/docs/configuration', '_self')}>
                      Configuration
                    </Button>
                    <Button variant="outline" className='justify-start' onClick={() => window.open('/docs/api', '_self')}>
                      API Reference
                    </Button>
                    <Button variant="outline" className='justify-start' onClick={() => window.open('/docs/troubleshooting', '_self')}>
                      Troubleshooting
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}