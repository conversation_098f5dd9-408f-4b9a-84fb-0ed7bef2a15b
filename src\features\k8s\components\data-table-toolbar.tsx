import { Cross2Icon } from '@radix-ui/react-icons'
import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useState, useEffect } from 'react'

interface DataTableToolbarProps<TData> {
  table: Table<TData>
  namespaces?: string[]
  showNamespaceFilter?: boolean
}

export function DataTableToolbar<TData>({
  table,
  namespaces = [],
  showNamespaceFilter = true,
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0
  const [selectedNamespace, setSelectedNamespace] = useState<string>('all')

  const namespaceColumnExists = !!table.getAllColumns().find(col => col.id === 'namespace');

  // Handle namespace selection
  const handleNamespaceChange = (value: string) => {
    setSelectedNamespace(value)
    if (value === 'all') {
      table.getColumn('namespace')?.setFilterValue(undefined)
    } else {
      table.getColumn('namespace')?.setFilterValue(value)
    }
  }

  // Reset selected namespace when filters are reset
  useEffect(() => {
    if (!isFiltered) {
      setSelectedNamespace('all')
    }
  }, [isFiltered])

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 flex-col-reverse items-start gap-y-2 sm:flex-row sm:items-center sm:space-x-2'>
        <div className='flex flex-col sm:flex-row gap-2'>
          <Input
            placeholder='Filter name...'
            value={(table.getColumn('name')?.getFilterValue() as string) ?? ''}
            onChange={(event) =>
              table.getColumn('name')?.setFilterValue(event.target.value)
            }
            className='h-8 w-[150px] lg:w-[200px]'
          />
          
          
          {showNamespaceFilter && namespaceColumnExists && namespaces.length > 0 && (
            <Select value={selectedNamespace} onValueChange={handleNamespaceChange}>
              <SelectTrigger className='h-8 w-[150px]'>
                <SelectValue placeholder="All namespaces" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All namespaces</SelectItem>
                {namespaces.map(ns => (
                  <SelectItem key={ns} value={ns}>{ns}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
        
        {isFiltered && (
          <Button
            variant='ghost'
            onClick={() => {
              table.resetColumnFilters()
            }}
            className='h-8 px-2 lg:px-3'
          >
            Reset
            <Cross2Icon className='ml-2 h-4 w-4' />
          </Button>
        )}
      </div>
      
    </div>
  )
}