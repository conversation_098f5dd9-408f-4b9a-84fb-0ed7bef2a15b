import React, { useState, useRef, useEffect } from 'react';
import Editor from "@monaco-editor/react";
import YAML from 'yaml';
// Dialog components removed - using custom modal
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { IconCopy, IconDeviceFloppy, IconEye } from '@tabler/icons-react';
import axios_api from '@/lib/axios_api';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Shield, Network, Trash2, Edit3, Save, X } from 'lucide-react';
import NetworkPolicySelector from './components/NetworkPolicySelector';

interface NetworkYamlEditorProps {
  open: boolean;
  yamlContent: string;
  onClose: () => void;
  defaultView?: 'yaml' | 'table';
}

// Network Policy parsed data interfaces
interface NetworkPolicyRule {
  id: number;
  type: 'ingress' | 'egress';
  source: string;
  sourceType: 'pod' | 'namespace' | 'external' | 'any';
  sourceNamespace?: string; // Add namespace info for pods
  destination: string;
  destinationType: 'pod' | 'namespace' | 'external' | 'any';
  destinationNamespace?: string; // Add namespace info for pods
  port: string;
  protocol: 'TCP' | 'UDP' | 'Any';
  description: string;
}

interface ParsedNetworkPolicy {
  name: string;
  namespace: string;
  podSelector: any;
  policyTypes: string[];
  rules: NetworkPolicyRule[];
}

/**
 * Parse single Network Policy object to extract rules
 */
const parseSingleNetworkPolicy = (policy: any, ruleIdOffset: number = 0): { rules: NetworkPolicyRule[], nextRuleId: number, policyInfo: { name: string, namespace: string, podSelector: any, policyTypes: string[] } } => {
  const rules: NetworkPolicyRule[] = [];
  let ruleId = ruleIdOffset + 1;

  // Parse ingress rules
  if (policy.spec?.ingress) {
    policy.spec.ingress.forEach((ingressRule: any, index: number) => {
      const ports = ingressRule.ports || [];
      const portString = ports.length > 0
        ? ports.map((p: any) => p.port || 'Any').join(', ')
        : 'Any';

      const protocol = ports.length > 0 && ports[0].protocol ? ports[0].protocol.toUpperCase() : 'Any';

      if (ingressRule.from && ingressRule.from.length > 0) {
        ingressRule.from.forEach((from: any) => {
          let source = 'Any';
          let sourceType: 'pod' | 'namespace' | 'external' | 'any' = 'any';

          if (from.podSelector) {
            const labels = from.podSelector.matchLabels || {};
            source = Object.keys(labels).length > 0
              ? Object.entries(labels).map(([k, v]) => `${k}=${v}`).join(', ')
              : 'All Pods';
            sourceType = 'pod';
          } else if (from.namespaceSelector) {
            const labels = from.namespaceSelector.matchLabels || {};
            source = Object.keys(labels).length > 0
              ? `NS: ${Object.entries(labels).map(([k, v]) => `${k}=${v}`).join(', ')}`
              : 'All Namespaces';
            sourceType = 'namespace';
          } else if (from.ipBlock) {
            source = from.ipBlock.cidr || 'External';
            sourceType = 'external';
          }

          const targetLabels = policy.spec.podSelector?.matchLabels || {};
          const destination = Object.keys(targetLabels).length > 0
            ? Object.entries(targetLabels).map(([k, v]) => `${k}=${v}`).join(', ')
            : 'All Pods';

          rules.push({
            id: ruleId++,
            type: 'ingress',
            source,
            sourceType,
            destination,
            destinationType: 'pod',
            port: portString,
            protocol: protocol as 'TCP' | 'UDP' | 'Any',
            description: `Ingress rule ${index + 1} - Allow traffic from ${sourceType}`
          });
        });
      } else {
        // No from specified means allow from anywhere
        const targetLabels = policy.spec.podSelector?.matchLabels || {};
        const destination = Object.keys(targetLabels).length > 0
          ? Object.entries(targetLabels).map(([k, v]) => `${k}=${v}`).join(', ')
          : 'All Pods';

        rules.push({
          id: ruleId++,
          type: 'ingress',
          source: 'Any',
          sourceType: 'any',
          destination,
          destinationType: 'pod',
          port: portString,
          protocol: protocol as 'TCP' | 'UDP' | 'Any',
          description: `Ingress rule ${index + 1} - Allow traffic from anywhere`
        });
      }
    });
  }

  // Parse egress rules
  if (policy.spec?.egress) {
    policy.spec.egress.forEach((egressRule: any, index: number) => {
      const ports = egressRule.ports || [];
      const portString = ports.length > 0
        ? ports.map((p: any) => p.port || 'Any').join(', ')
        : 'Any';

      const protocol = ports.length > 0 && ports[0].protocol ? ports[0].protocol.toUpperCase() : 'Any';

      const sourceLabels = policy.spec.podSelector?.matchLabels || {};
      const source = Object.keys(sourceLabels).length > 0
        ? Object.entries(sourceLabels).map(([k, v]) => `${k}=${v}`).join(', ')
        : 'All Pods';

      if (egressRule.to && egressRule.to.length > 0) {
        egressRule.to.forEach((to: any) => {
          let destination = 'Any';
          let destinationType: 'pod' | 'namespace' | 'external' | 'any' = 'any';

          if (to.podSelector) {
            const labels = to.podSelector.matchLabels || {};
            destination = Object.keys(labels).length > 0
              ? Object.entries(labels).map(([k, v]) => `${k}=${v}`).join(', ')
              : 'All Pods';
            destinationType = 'pod';
          } else if (to.namespaceSelector) {
            const labels = to.namespaceSelector.matchLabels || {};
            destination = Object.keys(labels).length > 0
              ? `NS: ${Object.entries(labels).map(([k, v]) => `${k}=${v}`).join(', ')}`
              : 'All Namespaces';
            destinationType = 'namespace';
          } else if (to.ipBlock) {
            destination = to.ipBlock.cidr || 'External';
            destinationType = 'external';
          }

          rules.push({
            id: ruleId++,
            type: 'egress',
            source,
            sourceType: 'pod',
            destination,
            destinationType,
            port: portString,
            protocol: protocol as 'TCP' | 'UDP' | 'Any',
            description: `Egress rule ${index + 1} - Allow traffic to ${destinationType}`
          });
        });
      } else {
        // No to specified means allow to anywhere
        rules.push({
          id: ruleId++,
          type: 'egress',
          source,
          sourceType: 'pod',
          destination: 'Any',
          destinationType: 'any',
          port: portString,
          protocol: protocol as 'TCP' | 'UDP' | 'Any',
          description: `Egress rule ${index + 1} - Allow traffic to anywhere`
        });
      }
    });
  }

  return {
    rules,
    nextRuleId: ruleId,
    policyInfo: {
      name: policy.metadata?.name || 'Unknown',
      namespace: policy.metadata?.namespace || 'default',
      podSelector: policy.spec?.podSelector || {},
      policyTypes: policy.spec?.policyTypes || []
    }
  };
};

/**
 * Parse Network Policy YAML to extract rules in firewall format
 * Supports both single policies and multi-document YAML (separated by ---)
 */
const parseNetworkPolicy = (yamlContent: string): ParsedNetworkPolicy | null => {
  try {
    // Split by document separator to handle multiple policies
    const documents = yamlContent.split(/^---\s*$/m).filter(doc => doc.trim());

    if (documents.length === 0) {
      return null;
    }

    const allRules: NetworkPolicyRule[] = [];
    let combinedPolicyTypes: string[] = [];
    let primaryPolicy: any = null;
    let ruleIdOffset = 0;

    // Parse each document
    for (const doc of documents) {
      const policy = YAML.parse(doc.trim());

      if (!policy || policy.kind !== 'NetworkPolicy') {
        continue;
      }

      // Use the first policy as the primary one for metadata
      if (!primaryPolicy) {
        primaryPolicy = policy;
      }

      const { rules, nextRuleId, policyInfo } = parseSingleNetworkPolicy(policy, ruleIdOffset);
      allRules.push(...rules);
      ruleIdOffset = nextRuleId - 1;

      // Combine policy types
      if (policyInfo.policyTypes) {
        combinedPolicyTypes = [...new Set([...combinedPolicyTypes, ...policyInfo.policyTypes])];
      }
    }

    if (!primaryPolicy || allRules.length === 0) {
      return null;
    }

    return {
      name: primaryPolicy.metadata?.name || 'Unknown',
      namespace: primaryPolicy.metadata?.namespace || 'default',
      podSelector: primaryPolicy.spec?.podSelector || {},
      policyTypes: combinedPolicyTypes,
      rules: allRules
    };
  } catch (error) {
    console.error('Error parsing network policy:', error);
    return null;
  }
};

/**
 * NetworkYamlEditor component for viewing and editing Kubernetes network policy YAML
 */
const NetworkYamlEditor: React.FC<NetworkYamlEditorProps> = ({
  open,
  yamlContent,
  onClose,
  defaultView = 'yaml'
}) => {
  // State management
  const [yaml, setYaml] = useState<string>(yamlContent);
  const [isEditable, setIsEditable] = useState<boolean>(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState<boolean>(false);
  const [isValidYaml, setIsValidYaml] = useState<boolean>(true);
  const [showParsedView, setShowParsedView] = useState<boolean>(defaultView === 'table');
  const [parsedPolicy, setParsedPolicy] = useState<ParsedNetworkPolicy | null>(null);
  const [editingRuleId, setEditingRuleId] = useState<number | null>(null);
  const [editedRules, setEditedRules] = useState<NetworkPolicyRule[]>([]);

  // Editor reference
  const editorRef = useRef<any>(null);

  // Update YAML content when prop changes
  useEffect(() => {
    setYaml(yamlContent);
    // Parse the YAML when it changes
    const parsed = parseNetworkPolicy(yamlContent);
    setParsedPolicy(parsed);
    if (parsed) {
      setEditedRules([...parsed.rules]);
    }
  }, [yamlContent]);

  // Handle ESC key to close modal
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && open) {
        onClose();
      }
    };

    if (open) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden'; // Prevent background scroll
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [open, onClose]);

  // Handle editor initialization
  const handleEditorDidMount = (editor: any) => {
    editorRef.current = editor;
  };

  // Validate and update YAML content
  const handleYamlChange = (value: string | undefined) => {
    if (!value) return;

    try {
      // Validate YAML syntax
      YAML.parse(value);
      setYaml(value);
      setIsValidYaml(true);
      // Re-parse when YAML changes
      const parsed = parseNetworkPolicy(value);
      setParsedPolicy(parsed);
    } catch (error) {
      console.error("Invalid YAML:", error);
      setYaml(value);
      setIsValidYaml(false);
      setParsedPolicy(null);
    }
  };

  // Copy YAML to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(yaml)
      .then(() => toast.success("YAML copied to clipboard"))
      .catch(() => toast.error("Failed to copy YAML"));
  };

  // Apply YAML to create/update network policy
  const handleApply = async () => {
    try {
       await axios_api.post("/k8s/network-policy/apply", yaml, {
        headers: {
          "Content-Type": "text/yaml",
        },
      });

      toast.success("Network policy successfully applied");
      setShowConfirmDialog(false);
      setIsEditable(false);
      onClose();
    } catch (error: any) {
      console.error("Error applying network policy:", error);
      toast.error(`Failed to apply network policy: ${error.response?.data?.message || error.message}`);
    }
  };

  // Handle rule editing
  const handleEditRule = (ruleId: number) => {
    // Cancel any other editing first
    if (editingRuleId !== null && editingRuleId !== ruleId) {
      handleCancelEdit();
    }
    setEditingRuleId(ruleId);
  };

  const handleSaveRule = (ruleId: number) => {
    setEditingRuleId(null);
    // Regenerate YAML from current edited rules
    regenerateYamlFromRules(editedRules);
    toast.success("Rule updated successfully");
  };

  const handleCancelEdit = () => {
    setEditingRuleId(null);
    // Reset edited rules to original
    if (parsedPolicy) {
      setEditedRules([...parsedPolicy.rules]);
      // Reset YAML to original
      const parsed = parseNetworkPolicy(yamlContent);
      if (parsed) {
        regenerateYamlFromRules(parsed.rules);
      }
    }
  };

  const handleDeleteRule = (ruleId: number) => {
    const updatedRules = editedRules.filter(rule => rule.id !== ruleId);
    setEditedRules(updatedRules);
    // Regenerate YAML from updated rules
    regenerateYamlFromRules(updatedRules);
    toast.success("Rule deleted successfully");
  };

  const handleRuleFieldChange = (ruleId: number, field: keyof NetworkPolicyRule, value: string) => {
    setEditedRules(prevRules => {
      const updatedRules = prevRules.map(rule =>
        rule.id === ruleId ? { ...rule, [field]: value } : rule
      );
      return updatedRules;
    });

    // Don't regenerate YAML immediately, wait for save
    // This prevents constant YAML updates while user is still editing
  };

  // Regenerate YAML from rules
  const regenerateYamlFromRules = (rules: NetworkPolicyRule[]) => {
    if (!parsedPolicy) return;

    const ingressRules = rules.filter(rule => rule.type === 'ingress');
    const egressRules = rules.filter(rule => rule.type === 'egress');

    // Determine policy types based on available rules
    const policyTypes: string[] = [];
    if (ingressRules.length > 0) policyTypes.push('Ingress');
    if (egressRules.length > 0) policyTypes.push('Egress');

    // Create a unified policy name if we have both types
    const policyName = (ingressRules.length > 0 && egressRules.length > 0)
      ? parsedPolicy.name.replace(/-allow-(ingress|egress).*/, '-unified-policy')
      : parsedPolicy.name;

    const yamlObj = {
      apiVersion: "networking.k8s.io/v1",
      kind: "NetworkPolicy",
      metadata: {
        name: policyName,
        namespace: parsedPolicy.namespace,
        labels: {
          "app.kubernetes.io/part-of": "kubepakt"
        }
      },
      spec: {
        podSelector: parsedPolicy.podSelector,
        policyTypes: policyTypes,
        ...(ingressRules.length > 0 && {
          ingress: ingressRules.map(rule => ({
            ...(rule.port !== 'Any' && {
              ports: rule.port.split(', ').map(portStr => {
                return {
                  ...(portStr !== 'Any' && { port: isNaN(Number(portStr)) ? portStr : Number(portStr) }),
                  ...(rule.protocol !== 'Any' && { protocol: rule.protocol })
                };
              })
            }),
            ...(rule.source !== 'Any' && {
              from: [{
                ...(rule.sourceType === 'pod' && rule.sourceNamespace && {
                  namespaceSelector: {
                    matchLabels: { 'kubernetes.io/metadata.name': rule.sourceNamespace }
                  },
                  podSelector: {
                    matchLabels: (() => {
                      // Parse label format: if it contains '=', split it, otherwise assume it's app label
                      if (rule.source.includes('=')) {
                        const [key, value] = rule.source.split('=');
                        return { [key]: value };
                      }
                      return { app: rule.source };
                    })()
                  }
                }),
                ...(rule.sourceType === 'pod' && !rule.sourceNamespace && {
                  podSelector: {
                    matchLabels: (() => {
                      // Parse label format: if it contains '=', split it, otherwise assume it's app label
                      if (rule.source.includes('=')) {
                        const [key, value] = rule.source.split('=');
                        return { [key]: value };
                      }
                      return { app: rule.source };
                    })()
                  }
                }),
                ...(rule.sourceType === 'namespace' && {
                  namespaceSelector: {
                    matchLabels: { 'kubernetes.io/metadata.name': rule.source }
                  }
                }),
                ...(rule.sourceType === 'external' && {
                  ipBlock: {
                    cidr: rule.source
                  }
                })
              }]
            })
          }))
        }),
        ...(egressRules.length > 0 && {
          egress: egressRules.map(rule => ({
            ...(rule.port !== 'Any' && {
              ports: rule.port.split(', ').map(portStr => {
                return {
                  ...(portStr !== 'Any' && { port: isNaN(Number(portStr)) ? portStr : Number(portStr) }),
                  ...(rule.protocol !== 'Any' && { protocol: rule.protocol })
                };
              })
            }),
            ...(rule.destination !== 'Any' && {
              to: [{
                ...(rule.destinationType === 'pod' && rule.destinationNamespace && {
                  namespaceSelector: {
                    matchLabels: { 'kubernetes.io/metadata.name': rule.destinationNamespace }
                  },
                  podSelector: {
                    matchLabels: (() => {
                      // Parse label format: if it contains '=', split it, otherwise assume it's app label
                      if (rule.destination.includes('=')) {
                        const [key, value] = rule.destination.split('=');
                        return { [key]: value };
                      }
                      return { app: rule.destination };
                    })()
                  }
                }),
                ...(rule.destinationType === 'pod' && !rule.destinationNamespace && {
                  podSelector: {
                    matchLabels: (() => {
                      // Parse label format: if it contains '=', split it, otherwise assume it's app label
                      if (rule.destination.includes('=')) {
                        const [key, value] = rule.destination.split('=');
                        return { [key]: value };
                      }
                      return { app: rule.destination };
                    })()
                  }
                }),
                ...(rule.destinationType === 'namespace' && {
                  namespaceSelector: {
                    matchLabels: { 'kubernetes.io/metadata.name': rule.destination }
                  }
                }),
                ...(rule.destinationType === 'external' && {
                  ipBlock: {
                    cidr: rule.destination
                  }
                })
              }]
            })
          }))
        })
      }
    };

    const newYaml = YAML.stringify(yamlObj);
    setYaml(newYaml);
  };

  // Render parsed network policy rules in Palo Alto firewall style table
  const renderParsedView = () => {
    if (!parsedPolicy) {
      return (
        <div className="text-center text-muted-foreground py-8">
          <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>Unable to parse network policy</p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {/* Policy Header */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <Network className="h-5 w-5" />
              {parsedPolicy.name}
            </CardTitle>
            <div className="flex gap-2 text-sm text-muted-foreground">
              <Badge variant="outline">Namespace: {parsedPolicy.namespace}</Badge>
              {parsedPolicy.policyTypes.map((type) => (
                <Badge key={type} variant="secondary">{type}</Badge>
              ))}
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-sm">
              <strong>Pod Selector:</strong> {JSON.stringify(parsedPolicy.podSelector.matchLabels || {}, null, 2)}
            </div>
          </CardContent>
        </Card>

        {/* Firewall Rules Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Security Rules ({parsedPolicy.rules.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            {parsedPolicy.rules.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No security rules defined
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow className="bg-muted/50">
                    <TableHead className="w-12">#</TableHead>
                    <TableHead className="w-16">Type</TableHead>
                    <TableHead className="min-w-[200px]">Source</TableHead>
                    <TableHead className="min-w-[200px]">Destination</TableHead>
                    <TableHead className="w-24">Port</TableHead>
                    <TableHead className="w-20">Protocol</TableHead>
                    <TableHead className="min-w-[150px]">Description</TableHead>
                    <TableHead className="w-24">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {editedRules.map((rule) => {
                    const isEditing = editingRuleId === rule.id;
                    return (
                      <TableRow key={rule.id} className="hover:bg-muted/30">
                        <TableCell className="font-mono text-sm">{rule.id}</TableCell>
                        <TableCell>
                          <Badge
                            variant={rule.type === 'ingress' ? 'default' : 'secondary'}
                            className="text-xs"
                          >
                            {rule.type === 'ingress' ? 'IN' : 'OUT'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <NetworkPolicySelector
                            key={`source-${rule.id}`}
                            type="source"
                            ruleType={rule.type}
                            currentValue={rule.source}
                            currentType={rule.sourceType}
                            onChange={(value, type, namespace) => {
                              handleRuleFieldChange(rule.id, 'source', value);
                              handleRuleFieldChange(rule.id, 'sourceType', type);
                              if (namespace) {
                                handleRuleFieldChange(rule.id, 'sourceNamespace', namespace);
                              }
                            }}
                            disabled={!isEditing}
                          />
                        </TableCell>
                        <TableCell>
                          <NetworkPolicySelector
                            key={`destination-${rule.id}`}
                            type="destination"
                            ruleType={rule.type}
                            currentValue={rule.destination}
                            currentType={rule.destinationType}
                            onChange={(value, type, namespace) => {
                              handleRuleFieldChange(rule.id, 'destination', value);
                              handleRuleFieldChange(rule.id, 'destinationType', type);
                              if (namespace) {
                                handleRuleFieldChange(rule.id, 'destinationNamespace', namespace);
                              }
                            }}
                            disabled={!isEditing}
                          />
                        </TableCell>
                        <TableCell>
                          {isEditing ? (
                            <Input
                              value={rule.port}
                              onChange={(e) => handleRuleFieldChange(rule.id, 'port', e.target.value)}
                              className="text-sm h-8 w-20"
                              placeholder="Port"
                            />
                          ) : (
                            <Badge variant="outline" className="text-xs font-mono">
                              {rule.port}
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          {isEditing ? (
                            <Select
                              value={rule.protocol}
                              onValueChange={(value) => handleRuleFieldChange(rule.id, 'protocol', value)}
                            >
                              <SelectTrigger className="w-16 h-8">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="TCP">TCP</SelectItem>
                                <SelectItem value="UDP">UDP</SelectItem>
                                <SelectItem value="Any">Any</SelectItem>
                              </SelectContent>
                            </Select>
                          ) : (
                            <Badge variant="outline" className="text-xs">
                              {rule.protocol}
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          {isEditing ? (
                            <Input
                              value={rule.description}
                              onChange={(e) => handleRuleFieldChange(rule.id, 'description', e.target.value)}
                              className="text-sm h-8"
                              placeholder="Description"
                            />
                          ) : (
                            <span className="text-sm text-muted-foreground">
                              {rule.description}
                            </span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            {isEditing ? (
                              <>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleSaveRule(rule.id)}
                                  className="h-8 w-8 p-0"
                                >
                                  <Save className="h-4 w-4 text-green-600" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={handleCancelEdit}
                                  className="h-8 w-8 p-0"
                                >
                                  <X className="h-4 w-4 text-red-600" />
                                </Button>
                              </>
                            ) : (
                              <>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleEditRule(rule.id)}
                                  className="h-8 w-8 p-0"
                                >
                                  <Edit3 className="h-4 w-4 text-blue-600" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleDeleteRule(rule.id)}
                                  className="h-8 w-8 p-0"
                                >
                                  <Trash2 className="h-4 w-4 text-red-600" />
                                </Button>
                              </>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    );
  };

  return (
    <>
      {/* Main YAML Editor Dialog */}
      {open && (
        <div
          className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm"
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              onClose();
            }
          }}
        >
          <div className="fixed inset-0 w-full h-full bg-background flex flex-col" onClick={(e) => e.stopPropagation()}>
            <div className="p-6 border-b">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-semibold">Network Policy Configuration</h2>
                  <p className="text-sm text-muted-foreground">
                    {showParsedView
                      ? "Network policy rules displayed in firewall format"
                      : "View or edit the Kubernetes Network Policy YAML configuration"
                    }
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowParsedView(!showParsedView)}
                  >
                    <IconEye size={16} className="mr-1" />
                    {showParsedView ? "Show YAML" : "Show Rules"}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onClose}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

          <div className="flex-1 p-6 overflow-auto">
            {showParsedView ? (
              renderParsedView()
            ) : (
              <Editor
                width="100%"
                height="100%"
                language="yaml"
                value={yaml}
                theme="vs-dark"
                options={{
                  selectOnLineNumbers: true,
                  roundedSelection: false,
                  readOnly: !isEditable,
                  cursorStyle: 'line',
                  automaticLayout: true,
                  wordWrap: 'on',
                  scrollBeyondLastLine: false,
                  minimap: { enabled: true },
                  lineNumbers: 'on',
                  folding: true,
                  renderLineHighlight: 'all',
                }}
                onChange={handleYamlChange}
                onMount={handleEditorDidMount}
              />
            )}
          </div>

          {!isValidYaml && isEditable && !showParsedView && (
            <div className="text-destructive text-sm">
              Warning: The YAML contains syntax errors
            </div>
          )}

            <div className="flex justify-between p-6 border-t">
              <div className="flex items-center space-x-2">
                {!showParsedView && (
                  <>
                    <Switch
                      id="edit-mode"
                      checked={isEditable}
                      onCheckedChange={setIsEditable}
                    />
                    <Label htmlFor="edit-mode">{isEditable ? "Edit Mode" : "View Mode"}</Label>
                  </>
                )}
              </div>

              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={copyToClipboard}
                >
                  <IconCopy size={16} className="mr-1" />
                  Copy YAML
                </Button>

                {isEditable && !showParsedView && (
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => setShowConfirmDialog(true)}
                    disabled={!isValidYaml}
                  >
                    <IconDeviceFloppy size={16} className="mr-1" />
                    Apply
                  </Button>
                )}

                <Button variant="secondary" size="sm" onClick={onClose}>
                  Close
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Confirmation Dialog */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Apply Network Policy</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to apply this network policy? This action will affect network traffic rules in your Kubernetes cluster.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleApply}>Apply</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default NetworkYamlEditor;