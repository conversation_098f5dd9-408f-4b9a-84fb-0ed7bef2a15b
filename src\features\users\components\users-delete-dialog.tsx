'use client'

import { useState } from 'react'
import { IconAlertTriangle } from '@tabler/icons-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ConfirmDialog } from '@/components/confirm-dialog'
import { User } from '../data/schema'
import { useUsers } from '../context/users-context'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow: User
  deleteUser?: (username: string) => Promise<void> // Add optional prop for testing/standalone usage
}

export function UsersDeleteDialog({ open, onOpenChange, currentRow, deleteUser: deleteUserProp }: Props) {
  const [value, setValue] = useState('')
  
  // Try to use context, but fall back to props if context is unavailable
  let contextValues
  try {
    contextValues = useUsers()
  } catch (error) {
    // Context not available, will use props instead
  }
  
  // Use the deleteUser from context if available, otherwise use the prop
  const deleteUserFn = contextValues?.deleteUser || deleteUserProp

  const handleDelete = async () => {
    if (value.trim() !== currentRow.username || !deleteUserFn) return

    try {
      await deleteUserFn(currentRow.username)
      onOpenChange(false)
    } catch (error) {
      // Error is already handled in the context
    } finally {
      setValue('')
    }
  }

  // Get the first role or use a default value
  const userRole = currentRow?.roles && currentRow.roles.length > 0 
    ? currentRow.roles.join(',').toUpperCase() 
    : 'USER'

  return (
    <ConfirmDialog
      open={open}
      onOpenChange={onOpenChange}
      handleConfirm={handleDelete}
      disabled={value.trim() !== currentRow.username || !deleteUserFn}
      title={
        <span className='text-destructive'>
          <IconAlertTriangle
            className='stroke-destructive mr-1 inline-block'
            size={18}
          />{' '}
          Delete User
        </span>
      }
      desc={
        <div className='space-y-4'>
          <p className='mb-2'>
            Are you sure you want to delete{' '}
            <span className='font-bold'>{currentRow.username}</span>?
            <br />
            This action will permanently remove the user with the role of{' '}
            <span className='font-bold'>
              {userRole}
            </span>{' '}
            from the system. This cannot be undone.
          </p>

          <Label className='my-2'>
            Username:
            <Input
              value={value}
              onChange={(e) => setValue(e.target.value)}
              placeholder='Enter username to confirm deletion.'
            />
          </Label>

          <Alert variant='destructive'>
            <AlertTitle>Warning!</AlertTitle>
            <AlertDescription>
              Please be careful, this operation cannot be rolled back.
            </AlertDescription>
          </Alert>
        </div>
      }
      confirmText='Delete'
      destructive
    />
  )
}
