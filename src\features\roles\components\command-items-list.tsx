import { useState, useMemo } from 'react'
import { useRoles } from '../context/roles-context'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { IconPlus, IconTrash, IconSearch } from '@tabler/icons-react'
import { Card, CardContent } from '@/components/ui/card'
import { CommandAllowlistItem } from '../data/schema'
import DeleteCommandDialog from './delete-command-dialog'

export default function CommandItemsList() {
  const { commandItems, currentRole, setOpen } = useRoles()
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [commandToDelete, setCommandToDelete] = useState<CommandAllowlistItem | null>(null)
  const [filterText, setFilterText] = useState('')

  const handleDeleteClick = (item: CommandAllowlistItem) => {
    setCommandToDelete(item)
    setDeleteDialogOpen(true)
  }

  // Filter commands based on command name and description
  const filteredCommands = useMemo(() => {
    if (!filterText.trim()) {
      return commandItems
    }

    const searchTerm = filterText.toLowerCase().trim()
    return commandItems.filter(item =>
      item.commandName.toLowerCase().includes(searchTerm) ||
      (item.description && item.description.toLowerCase().includes(searchTerm))
    )
  }, [commandItems, filterText])

  if (!currentRole) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            Select a role from the Roles tab to view its command permissions
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium">
          Command Permissions for: <span className="font-bold">{currentRole.roleName}</span>
        </h3>
        <Button
          size="sm"
          onClick={() => setOpen('add')}
        >
          <IconPlus className="mr-2 h-4 w-4" />
          Add Command
        </Button>
      </div>

      {/* Search/Filter Input */}
      <div className="mb-4">
        <div className="relative">
          <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Filter by command name or description..."
            value={filterText}
            onChange={(e) => setFilterText(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {commandItems.length === 0 ? (
        <div className="text-center p-4 border rounded-md bg-muted/20">
          No command permissions found for this role
        </div>
      ) : filteredCommands.length === 0 ? (
        <div className="text-center p-4 border rounded-md bg-muted/20">
          No commands found matching "{filterText}"
        </div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Command Name</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCommands.map((item) => (
              <TableRow key={`${item.roleName}-${item.commandName}`}>
                <TableCell className="font-medium">{item.commandName}</TableCell>
                <TableCell>{item.description || 'No description'}</TableCell>
                <TableCell>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteClick(item)}
                    className="text-destructive hover:text-destructive"
                  >
                    <IconTrash className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}

      <DeleteCommandDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        commandItem={commandToDelete}
      />
    </div>
  )
}