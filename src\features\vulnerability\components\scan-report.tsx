import React, { useState, useEffect } from 'react';
import {
    <PERSON>,
    CardHeader,
    CardTitle,
    CardDescription,
    CardContent,
} from "@/components/ui/card";
import {
    Table,
    TableHeader,
    TableRow,
    TableHead,
    TableBody,
    TableCell,
    TableCaption
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area"
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/ui/accordion"
import { CalendarIcon } from "@radix-ui/react-icons"
import axios_api from '@/lib/axios_api';


interface ScanReportProps {
    image: string;
    id: number;
}

interface Vulnerability {
    id: string;
    name: string;
    severity: string;
    description?: string;
    solution?: string;
    location?: {
        dependency?: {
            package?: {
                name?: string;
            };
            version?: string;
        };
        operating_system?: string;
        image?: string;
    };
    identifiers?: Array<{
        type: string;
        value: string;
        url: string;
    }>;
    links?: Array<{
        url: string;
    }>;
}

interface ScanData {
    version?: string;
    scan?: {
        analyzer?: {
            name?: string;
        };
        status?: string;
        start_time?: string;
    };
    last?: string;
    vulnerabilities?: Vulnerability[];
}

function ScanReport({ image, id }: ScanReportProps) {
    const [scanData, setScanData] = useState<ScanData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<any|null>(null);

    useEffect(() => {
        const fetchScanData = async () => {
            try {
                const response = await axios_api.get(`/vulnresults/${id}`); // API endpoint'inizi buraya girin               
                const data = await response.data;
                setScanData(data);
                setLoading(false);
            } catch (e) {
                setLoading(false);
            }
        };

        fetchScanData();
    }, [id]);

    if (loading) {
        return <div className="flex items-center justify-center h-screen">Loading...</div>;
    }

    if (error) {
        return <div className="flex items-center justify-center h-screen">Something went wrong: {error.message}</div>;
    }

    if (!scanData) {
        return <div className="flex items-center justify-center h-screen">No data found.</div>;
    }

    let resultJson = null;
    try {
        resultJson = scanData;
        console.log("Parse edilen resultJson:", resultJson);
    } catch (e) {
        setError("resultJson parse hatası: " + (e instanceof Error ? e.message : String(e)));
        return <div className="flex items-center justify-center h-screen">resultJson parse hatası: {(e instanceof Error ? e.message : String(e))}</div>;
    }

    const getSeverityColor = (severity: any) => {
        switch (severity) {
            case "Critical":
                return "bg-red-500 text-white";
            case "High":
                return "bg-orange-500 text-white";
            case "Medium":
                return "bg-yellow-500 text-black";
            case "Low":
                return "bg-green-500 text-white";
            default:
                return "bg-gray-500 text-white";
        }
    };

    const formatDate = (dateString?: string) => {
        if (!dateString) return "N/A";
        const date = new Date(dateString);
        return date.toLocaleDateString('tr-TR', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: 'numeric',
            minute: 'numeric',
            second: 'numeric'
        });
    };

    // Şiddet düzeyine göre sıralama fonksiyonu
    const sortVulnerabilities = (vulnerabilities: any) => {
        const severityOrder: { [key: string]: number } = {
            "Critical": 1,
            "High": 2,
            "Medium": 3,
            "Low": 4,
            "Informational": 5,
            "Unknown": 6,
        };

        return [...vulnerabilities].sort((a: { severity: string }, b: { severity: string }) => {
            const severityA = severityOrder[a.severity as string] || 100; // Tanımsız şiddetler için varsayılan değer
            const severityB = severityOrder[b.severity as string] || 100;
            return severityA - severityB;
        });
    };

    // Sıralanmış güvenlik açıkları
    const sortedVulnerabilities = resultJson?.vulnerabilities ? sortVulnerabilities(resultJson.vulnerabilities) : [];

    return (
        <div className=" mx-auto p-4 h-full overflow-x-auto ">
            <h1 className="text-3xl font-bold mb-6 text-gray-800">Scan Report</h1>


            <Card className="mb-6">
                <CardHeader>
                    <CardTitle className="text-lg font-semibold flex items-center"><CalendarIcon className="mr-2 h-4 w-4" /> Tarama Detayları</CardTitle>
                    <CardDescription className="text-sm text-gray-500">Versiyon ve tarama motoru bilgileri</CardDescription>
                </CardHeader>
                <CardContent>
                    <p className="mb-2"><span className="font-semibold">Image:</span> {image}</p>
                    {resultJson && (
                        <>
                            <p className="mb-2"><span className="font-semibold">Versiyon:</span> {resultJson.version}</p>
                            {resultJson.scan && resultJson.scan.analyzer && (
                                <>
                                    <p className="mb-2"><span className="font-semibold">Tarama Motoru:</span> {resultJson.scan.analyzer.name}</p>
                                    <p className="mb-2"><span className="font-semibold">Tarama Durumu:</span> {resultJson.scan.status}</p>
                                    <p className="mb-2"><span className="font-semibold">Başlangıç Zamanı:</span> {formatDate(resultJson.scan.start_time)}</p>
                                    <p className="mb-2"><span className="font-semibold">Bitiş Zamanı:</span> {formatDate(scanData.last)}</p>
                                </>
                            )}
                        </>
                    )}
                </CardContent>
            </Card>

            <div className="mb-6">
                <h2 className="text-2xl font-bold mb-4 text-gray-800">Güvenlik Açıkları</h2>

                <ScrollArea>
                    <Table>
                        <TableCaption>Tespit Edilen Güvenlik Açıkları</TableCaption>
                        <TableHeader>
                            <TableRow>
                                <TableHead>ID</TableHead>
                                <TableHead>Adı</TableHead>
                                <TableHead>Şiddeti</TableHead>
                                <TableHead>Detaylar</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {sortedVulnerabilities.length > 0 ? (
                                sortedVulnerabilities.map((vulnerability) => {
                                    console.log("Vulnerability:", vulnerability);
                                    return (
                                        <TableRow key={vulnerability.id}>
                                            <TableCell className="font-medium">{vulnerability.id}</TableCell>
                                            <TableCell>{vulnerability.name}</TableCell>
                                            <TableCell>
                                                <Badge className={getSeverityColor(vulnerability.severity)}>
                                                    {vulnerability.severity}
                                                </Badge>
                                            </TableCell>
                                            <TableCell>
                                                <Accordion type="single" collapsible>
                                                    <AccordionItem value={vulnerability.id}>
                                                        <AccordionTrigger>More information</AccordionTrigger>
                                                        <AccordionContent>
                                                            <p className="mb-2"><span className="font-semibold">Açıklama:</span> {vulnerability.description}</p>
                                                            <p className="mb-2"><span className="font-semibold">Çözüm:</span> {vulnerability.solution}</p>
                                                            {vulnerability.location && (
                                                                <div className="mb-2">
                                                                    <span className="font-semibold">Konum:</span>
                                                                    <p className="ml-4"><span className="font-semibold">Bağımlılık:</span> {vulnerability.location.dependency?.package?.name} ({vulnerability.location.dependency?.version})</p>
                                                                    <p className="ml-4"><span className="font-semibold">İşletim Sistemi:</span> {vulnerability.location.operating_system}</p>
                                                                    <p className="ml-4"><span className="font-semibold">Image:</span> {vulnerability.location.image}</p>
                                                                </div>
                                                            )}

                                                            {vulnerability.identifiers && vulnerability.identifiers.length > 0 && (
                                                                <div className="mb-2">
                                                                    <span className="font-semibold">Tanımlayıcılar:</span>
                                                                    <ul className="list-disc pl-5">
                                                                        {vulnerability.identifiers.map((identifier: { url: string | undefined; type: string | number | bigint | boolean | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | Promise<string | number | bigint | boolean | React.ReactPortal | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | null | undefined> | null | undefined; value: string | number | bigint | boolean | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | Promise<string | number | bigint | boolean | React.ReactPortal | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | null | undefined> | null | undefined; }, index: React.Key | null | undefined) => (
                                                                            <li key={index}>
                                                                                <a href={identifier.url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
                                                                                    {identifier.type}: {identifier.value}
                                                                                </a>
                                                                            </li>
                                                                        ))}
                                                                    </ul>
                                                                </div>
                                                            )}

                                                            {vulnerability.links && vulnerability.links.length > 0 && (
                                                                <div>
                                                                    <span className="font-semibold">Bağlantılar:</span>
                                                                    <ul className="list-disc pl-5">
                                                                        {vulnerability.links.map((link:any, index:any) => (
                                                                            <li key={index}>
                                                                                <a href={link.url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
                                                                                    {link.url}
                                                                                </a>
                                                                            </li>
                                                                        ))}
                                                                    </ul>
                                                                </div>
                                                            )}
                                                        </AccordionContent>
                                                    </AccordionItem>
                                                </Accordion>
                                            </TableCell>
                                        </TableRow>
                                    );
                                })
                            ) : (
                                <TableRow>
                                    <TableCell colSpan={5} className="text-center">
                                        Güvenlik açığı bulunamadı.
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </ScrollArea>
            </div>
        </div>
    );
}

export default ScanReport;