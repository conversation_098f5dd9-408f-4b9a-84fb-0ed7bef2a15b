import { useState } from 'react'
import { DotsHorizontalIcon } from '@radix-ui/react-icons'
import { IconTrash, IconCopy, IconLabel, IconTagFilled, IconFileCode } from '@tabler/icons-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import { YamlMenuItem } from '../../components/yaml/yaml-viewer'
import axios_api from '@/lib/axios_api'

interface RowOriginal {
  name?: string;
  namespace?: string;
  labels?: Record<string, string>;
  annotation?: Record<string, string>;
}

interface DataTableRowActionsProps<TData extends { original: RowOriginal }> {
  row: TData;
}

export function DataTableRowActions<TData extends { original: RowOriginal }>({
  row,
}: DataTableRowActionsProps<TData>) {
  const [showLabelsModal, setShowLabelsModal] = useState(false)
  const [showAnnotationsModal, setShowAnnotationsModal] = useState(false)
  const [showYamlModal, setShowYamlModal] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)

  // @ts-ignore - We know these properties exist on the row
  const labels = row.original?.labels || {}
  // @ts-ignore - We know these properties exist on the row
  const annotations = row.original?.annotation || {}

  // Function to parse JSON string if needed
  const parseJsonIfString = (data: any) => {
    if (typeof data === 'string') {
      try {
        return JSON.parse(data)
      } catch (e) {
        return data
      }
    }
    return data
  }

  const _resourceType = "service";

  const parsedLabels = parseJsonIfString(labels)
  const parsedAnnotations = parseJsonIfString(annotations)

  // Function to copy content to clipboard
  const copyToClipboard = (content: string, type: string) => {
    navigator.clipboard.writeText(content)
      .then(() => toast.success(`${type} copied to clipboard`))
      .catch(() => toast.error(`Failed to copy ${type}`))
  }

  // Function to handle resource deletion
  const handleDelete = async () => {
    try {
      const response = await axios_api.delete('/k8s/delete', {
        params: {
          kind: _resourceType,
          namespace: row.original?.namespace,
          name: row.original?.name
        }
      });
      
      if (response.status >= 200 && response.status < 300) {
        toast.success(`${_resourceType.toUpperCase()} ${row.original?.name} deleted successfully`);
        setShowDeleteModal(false);
      } else {
        toast.error(`HTTP error! status: ${response.status}`);
      }
    } catch (err) {
    }
  }

  return (
    <>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button
            variant='ghost'
            className='data-[state=open]:bg-muted flex h-8 w-8 p-0'
          >
            <DotsHorizontalIcon className='h-4 w-4' />
            <span className='sr-only'>Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end' className='w-[160px]'>
          <DropdownMenuItem onClick={() => setShowYamlModal(true)}>
            Show Yaml
            <DropdownMenuShortcut>
              <IconFileCode size={16} />
            </DropdownMenuShortcut>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setShowLabelsModal(true)}>
            Show Labels
            <DropdownMenuShortcut>
              <IconLabel size={16} />
            </DropdownMenuShortcut>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setShowAnnotationsModal(true)}>
            Show Annotations
            <DropdownMenuShortcut>
              <IconTagFilled size={16} />
            </DropdownMenuShortcut>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => setShowDeleteModal(true)}>
            Delete
            <DropdownMenuShortcut>
              <IconTrash size={16} />
            </DropdownMenuShortcut>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Delete Confirmation Modal */}
      <Dialog open={showDeleteModal} onOpenChange={setShowDeleteModal}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete {_resourceType.toUpperCase()} </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the {_resourceType.toUpperCase()}  "{row.original?.name}" in namespace "{row.original?.namespace}"?
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteModal(false)}>Cancel</Button>
            <Button variant="destructive" onClick={handleDelete}>Delete</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Labels Modal */}
      <Dialog open={showLabelsModal} onOpenChange={setShowLabelsModal}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              {/* @ts-ignore - We know name exists on the row */}
              <span>Labels for {row.original?.name}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(JSON.stringify(parsedLabels, null, 2), "Labels")}
                className="h-8 px-2"
              >
                <IconCopy size={16} />
              </Button>
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-auto max-h-[60vh]">
            {typeof parsedLabels === 'string' ? (
              <pre className="bg-muted p-4 rounded-md overflow-x-auto text-sm">
                {parsedLabels}
              </pre>
            ) : Object.keys(parsedLabels).length > 0 ? (
              <div className="space-y-4">
                <div className="flex flex-wrap gap-2 mb-4">
                  {Object.entries(parsedLabels).map(([key, value]) => (
                    <Badge key={key} variant="outline" className="px-2 py-1 text-xs">
                      {key}: {String(value)}
                    </Badge>
                  ))}
                </div>

                <div className="bg-muted rounded-md p-4">
                  <h3 className="text-sm font-medium mb-2">Details</h3>
                  <div className="grid grid-cols-1 gap-2">
                    {Object.entries(parsedLabels).map(([key, value]) => (
                      <div key={key} className="flex items-center border-b pb-2">
                        <span className="font-medium min-w-[120px] text-sm">{key}:</span>
                        <span className="text-muted-foreground text-sm overflow-hidden text-ellipsis">{String(value)}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-muted rounded-md p-4">
                  <h3 className="text-sm font-medium mb-2">JSON</h3>
                  <pre className="text-xs overflow-x-auto">
                    {JSON.stringify(parsedLabels, null, 2)}
                  </pre>
                </div>
              </div>
            ) : (
              <p className="text-muted-foreground">No labels found</p>
            )}
          </div>
        </DialogContent>
      </Dialog>


      <YamlMenuItem
            resourceName={row.original?.name || ""}
            resourceNamespace={row.original?.namespace || ""}
            resourceType={_resourceType}
            showYamlModal={showYamlModal}
            setShowYamlModal={setShowYamlModal}
      />

      {/* Annotations Modal */}
      <Dialog open={showAnnotationsModal} onOpenChange={setShowAnnotationsModal}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              {/* @ts-ignore - We know name exists on the row */}
              <span>Annotations for {row.original?.name}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(JSON.stringify(parsedAnnotations, null, 2), "Annotations")}
                className="h-8 px-2"
              >
                <IconCopy size={16} />
              </Button>
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-auto max-h-[60vh]">
            {typeof parsedAnnotations === 'string' ? (
              <pre className="bg-muted p-4 rounded-md overflow-x-auto text-sm">
                {parsedAnnotations}
              </pre>
            ) : Object.keys(parsedAnnotations).length > 0 ? (
              <div className="space-y-4">
                <div className="flex flex-wrap gap-2 mb-4">
                  {Object.entries(parsedAnnotations).map(([key, value]) => (
                    <Badge key={key} variant="outline" className="px-2 py-1 text-xs">
                      {key}: {String(value).substring(0, 20)}{String(value).length > 20 ? '...' : ''}
                    </Badge>
                  ))}
                </div>

                <div className="bg-muted rounded-md p-4">
                  <h3 className="text-sm font-medium mb-2">Details</h3>
                  <div className="grid grid-cols-1 gap-2">
                    {Object.entries(parsedAnnotations).map(([key, value]) => (
                      <div key={key} className="flex flex-col border-b pb-2">
                        <span className="font-medium text-sm">{key}:</span>
                        <span className="text-muted-foreground text-sm break-words">{String(value)}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-muted rounded-md p-4">
                  <h3 className="text-sm font-medium mb-2">JSON</h3>
                  <pre className="text-xs overflow-x-auto">
                    {JSON.stringify(parsedAnnotations, null, 2)}
                  </pre>
                </div>
              </div>
            ) : (
              <p className="text-muted-foreground">No annotations found</p>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
