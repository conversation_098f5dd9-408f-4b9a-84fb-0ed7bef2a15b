import { useEffect, useRef, useState } from "react";
import { Terminal } from "xterm";
import { FitAddon } from "xterm-addon-fit";
import "xterm/css/xterm.css";
import { useAuthStore } from "@/stores/authStore";

export interface PodConsoleProps {
    podName: string;
    namespace: string;
    isOpen?: boolean;
}

const ConsoleTerminal = ({ podName, namespace, isOpen = true } : PodConsoleProps) => {
    const terminalRef = useRef(null);
    const [socket, setSocket] = useState<WebSocket|null>(null);
    const term = useRef<Terminal|null>(null);
    const fitAddon = useRef(new FitAddon());

    useEffect(() => {
        if (!terminalRef.current) return;

        // Xterm terminali oluştur
        term.current = new Terminal({
            cursorBlink: true,
            fontSize: 14,
            theme: {
                background: "#1e1e1e",
                foreground: "#ffffff"
            }
        });

        term.current.loadAddon(fitAddon.current);
        term.current.open(terminalRef.current);
        fitAddon.current.fit(); // Terminali ekrana uydur

        // WebSocket bağlantısını başlat
        connectWebSocket();

        return () => {
            if (socket) {
                console.log("Closing WebSocket connection on terminal unmount");
                socket.close();
            }
            term.current?.dispose();
        };
    }, []);

    // Close socket when dialog closes
    useEffect(() => {
        if (!isOpen && socket) {
            console.log("Closing WebSocket connection on dialog close");
            socket.close();
            setSocket(null);
        }
    }, [isOpen, socket]);

    const connectWebSocket = () => {
        const token = useAuthStore.getState().auth.accessToken || '';
        const protocol = window.location.protocol === 'https:' ? 'wss' : 'ws';
        const ws = new WebSocket(`${protocol}://${window.location.host}/ws/terminal?token=${token}&podName=${podName}&namespace=${namespace}`);

        ws.onopen = () => {
            console.log("✅ WebSocket connection opened.");
            term.current?.writeln("Connection is successful Welcome to Kubernetes Pod!\n");
        };

        ws.onmessage = (event) => {
            term.current!.write(event.data);
        };

        ws.onerror = (error) => {
            console.error("❌ WebSocket error:", error);
            term.current!.writeln("\nERROR: WebSocket connection failed!\n");
        };

        ws.onclose = (event) => {
            console.warn(`⚠️ WebSocket connection closed. Code: ${event.code}, Reason: ${event.reason}`);
            term.current!.writeln("\nConnection closed. Try to reconnect.\n");
        };

        // Kullanıcının terminale girdiği komutları WebSocket üzerinden gönder
        term.current!.onData((data) => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(data);
            }
        });

        setSocket(ws);
    };

    return <div ref={terminalRef} style={{ width: "100%", height: "500px", backgroundColor: "#000" }} />;
};

export default ConsoleTerminal;
