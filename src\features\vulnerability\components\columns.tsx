import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Tooltip } from "@/components/ui/tooltip"; // Tooltip bileşeni varsa kullanabilirsin
import { DataTableColumnHeader } from "./data-table-column-header";

interface VulnResult {
  id: number;
  created: string;
  image: string;
  object: string;
  last: string;
  resultJson: string;
  low: number;
  high: number;
  crict: number;
  medium: number;
}

const severityColors = {
  crict: "bg-red-600 text-white shadow-md",
  high: "bg-orange-500 text-white shadow-md",
  medium: "bg-yellow-400 text-black shadow-md",
  low: "bg-green-600 text-white shadow-md",
};

const getSeverityColor = (key: keyof typeof severityColors) => severityColors[key] || "";

export const columns = (
  onDetailsClick: (vulnResult: VulnResult) => void
): ColumnDef<VulnResult>[] => [
    {
      accessorKey: "image",
       header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Image Name' />
      ),
      cell: ({ row }) => (
        <div className="truncate max-w-xs font-semibold text-gray-900" title={row.getValue("image")}>
          {row.getValue("image")}
        </div>
      ),
    },
    {
      accessorKey: "object",
       header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Object Type' />
      ),
      cell: ({ row }: { row: any }) => {
        const text = (row.getValue("object") as string) || "";
        const parts = text.split(",");
        return (
          <div className="max-w-xs leading-snug text-gray-700">
            {parts.map((part, idx) => (
              <span key={idx} className="block truncate" title={part.trim()}>
                {part.trim()}
              </span>
            ))}
          </div>
        );
      },
    },
    ...(["crict", "high", "medium", "low"] as (keyof typeof severityColors)[]).map((key) => ({
      accessorKey: key,
      header: ({ column }: import("@tanstack/react-table").HeaderContext<VulnResult, unknown>) => (
        <DataTableColumnHeader column={column} title={key.toUpperCase()} />
      ),
      cell: ({ row }: { row: import("@tanstack/react-table").Row<VulnResult> }) => {
        const value = row.getValue(key) as number;
        const colorClass = getSeverityColor(key);
        return (
          <Tooltip>
            <span className="sr-only">{`${key.toUpperCase()} severity count`}</span>
            <div
              className={`${colorClass} rounded-md px-3 py-1 text-center font-semibold text-sm min-w-[2rem]`}
              title={`${key.toUpperCase()} severity count`}
            >
              {value > 0 ? value : 0}
            </div>
          </Tooltip>
        );
      },
      enableSorting: true,
      enableHiding: true,
    })),
    {
      accessorKey: 'unknown',
      id: "unknown",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='N/A' />
      ),
      cell: () => (
        <div className="bg-gray-300 text-gray-600 rounded-md px-3 py-1 text-center  min-w-[2rem]">
          0
        </div>
      ),
      enableSorting: true,
      enableHiding: true,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <Button
          size="sm"
          variant="outline"
          className="hover:bg-blue-600 hover:text-white transition-colors duration-200"
          onClick={() => onDetailsClick(row.original)}
        >
          Details
        </Button>
      ),
    },
  ];
