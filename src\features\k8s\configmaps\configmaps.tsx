import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import axios_api from '@/lib/axios_api';
import { DataTable } from '../components/data-table';

import { Header } from '@/components/layout/header';
import { ThemeSwitch } from '@/components/theme-switch';
import { ProfileDropdown } from '@/components/profile-dropdown';
import { Search } from '@/components/search';
import { Main } from '@/components/layout/main';
import { columns } from './components/columns';

const Configmaps: React.FC = () => {
  const [configmaps, setConfigmaps] = useState<[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchConfigmaps = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axios_api.get('/k8s/configmaps');
      if (response.status >= 200 && response.status < 300) {
        if (response?.data) {
          setConfigmaps(response.data);
          toast.success('ConfigMaps loaded successfully');
        } else {
          toast.error('Response data missing or in incorrect format.');
        }
      } else {
        toast.error(`HTTP error! status: ${response.status}`);
      }
    } catch (err) {
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchConfigmaps();
  }, [fetchConfigmaps]);

  return (
    <>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>
      <Main fixed>
        <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
          <DataTable 
            data={configmaps} 
            columns={columns} 
            loading={loading}
            title="ConfigMaps"
            description="List of all Kubernetes ConfigMaps in the cluster"
          />
        </div>
      </Main>
    </>
  );
};

export default Configmaps;

