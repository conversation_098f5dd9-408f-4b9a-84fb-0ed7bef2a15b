import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useRoles } from '../context/roles-context'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { IconShieldPlus } from '@tabler/icons-react'

const formSchema = z.object({
  commandName: z.string().min(1, { message: 'Command name is required.' }),
  description: z.string().optional(),
})

type CommandItemForm = z.infer<typeof formSchema>

export default function AddCommandItemDialog() {
  const { open, setOpen, currentRole, addCommandItem } = useRoles()
  
  const form = useForm<CommandItemForm>({
    resolver: zod<PERSON>esolver(formSchema),
    defaultValues: { commandName: '', description: '' },
  })

  const onSubmit = async (values: CommandItemForm) => {
    if (!currentRole) return

    try {
      await addCommandItem({
        id: 0, // Temporary ID, will be assigned by backend
        roleName: currentRole.roleName,
        commandName: values.commandName,
        description: values.description || null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      })
      form.reset()
      setOpen(null)
    } catch (error) {
      console.error('Error adding command:', error)
    }
  }

  return (
    <Dialog
      open={open === 'add'}
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          form.reset()
          setOpen(null)
        }
      }}
    >
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="text-left">
          <DialogTitle className="flex items-center gap-2">
            <IconShieldPlus /> Add Command Permission
          </DialogTitle>
          <DialogDescription>
            {currentRole && (
              <>Add a new command permission for role: <strong>{currentRole.roleName}</strong></>
            )}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            id="add-command-form"
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-4"
          >
            <FormField
              control={form.control}
              name="commandName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Command Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter command name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Enter command description" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
        <DialogFooter>
          <Button type="submit" form="add-command-form">
            Add Command
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}