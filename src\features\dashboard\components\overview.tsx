import { <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'recharts'
import { useEffect, useState } from 'react'
import axios_api from '@/lib/axios_api'

export function Overview() {
  const [data, setData] = useState<any[]>([])

  useEffect(() => {
    async function fetchMetrics() {
      try {
        const response = await axios_api.get("/metrics/nodes")
        
        // Transform data to extract just the percentage values as numbers
        const transformedData = response.data.map((node: any) => ({
          nodeName: node.nodeName,
          cpuUsagePercent: parseFloat(node.cpuUsagePercent.replace('%', '')),
          memoryUsagePercent: parseFloat(node.memoryUsagePercent.replace('%', ''))
        }))
        
        setData(transformedData)
      } catch (err) {
      } 
    }

    fetchMetrics()
  }, [])

  return (
    <ResponsiveContainer width='100%' height={400}>
      <Bar<PERSON><PERSON> data={data} >
        <XAxis
          dataKey='nodeName'
          stroke='#888888'
          fontSize={12}
          tickLine={false}
          axisLine={false}
          angle={-60}
          textAnchor="end"
          height={100}
          interval={0}
        />
        <YAxis
          stroke='#888888'
          fontSize={12}
          tickLine={false}
          axisLine={false}
          tickFormatter={(value) => `${value}%`}
        />
        <Tooltip />
        <Legend 
          verticalAlign="bottom" 
          align="center"
          wrapperStyle={{ paddingBottom: 20 }}
        />
        <Bar
          name="CPU Usage"
          dataKey='cpuUsagePercent'
          fill='#8884d8'
          radius={[4, 4, 0, 0]}
        />
        <Bar
          name="Memory Usage"
          dataKey='memoryUsagePercent'
          fill='#82ca9d'
          radius={[4, 4, 0, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  )
}
