import { useState, useEffect } from 'react'
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { DataTablePagination } from './data-table-pagination'
import { DataTableToolbar } from './data-table-toolbar'

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  title?: string
  description?: string
  loading?: boolean
  onRowClick?: (row: TData) => void
  onCellClick?: (columnId: string, value: string) => void
}

export function DataTable<TData, TValue>({
  columns,
  data,
  title,
  description,
  loading = false,
  onRowClick,
  onCellClick,
}: DataTableProps<TData, TValue>) {
  const [rowSelection, setRowSelection] = useState({})
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [sorting, setSorting] = useState<SortingState>([])
  const [namespaces, setNamespaces] = useState<string[]>([])

  // Extract unique namespaces from the data
  useEffect(() => {
    if (data.length > 0) {
      const nsSet = new Set<string>()
      data.forEach((item: any) => {
        if (item.namespace && typeof item.namespace === 'string') {
          nsSet.add(item.namespace)
        }
      })
      setNamespaces(Array.from(nsSet).sort())
    }
  }, [data])

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    initialState: {
      pagination: {
        pageSize: 10, // Set default page size
      },
    },
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-2">Loading...</span>
      </div>
    )
  }

  // Handle cell click
  const handleCellClick = (columnId: string, value: any) => {
    if (onCellClick && value !== undefined && value !== null) {
      onCellClick(columnId, String(value));
    }
  };

  return (
    <div className='space-y-4 w-full'>
      {(title || description) && (
        <div>
          {title && <h1 className='text-2xl font-bold tracking-tight'>{title}</h1>}
          {description && <p className='text-muted-foreground'>{description}</p>}
        </div>
      )}
      
      <DataTableToolbar table={table} namespaces={namespaces} />
      
      <div className='rounded-md border w-full overflow-hidden'>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  onClick={() => onRowClick && onRowClick(row.original)}
                  className={onRowClick ? 'cursor-pointer hover:bg-muted/50' : ''}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell 
                      key={cell.id}
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent row click from firing
                        handleCellClick(cell.column.id, cell.getValue());
                      }}
                      className="cursor-pointer hover:bg-muted/30"
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className='h-24 text-center'
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      
      {/* Ensure pagination is visible */}
      <div className="mt-4 w-full">
        <DataTablePagination table={table} />
      </div>
    </div>
  )
}
