import React, { useEffect, useState, useCallback, useRef } from 'react';
import * as d3 from 'd3';
import { IconZoomIn, IconZoomOut, IconArrowsMaximize } from '@tabler/icons-react';
import { useNavigate } from '@tanstack/react-router';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import axios_api from '@/lib/axios_api';
import { Main } from '@/components/layout/main';
import { Header } from '@/components/layout/header';
import { Search } from '@/components/search';
import { ThemeSwitch } from '@/components/theme-switch';
import { ProfileDropdown } from '@/components/profile-dropdown';
import { useTheme } from '@/context/theme-context';

// Interface definitions
interface NamespaceData {
    id: string;
    name: string;
    labels: string;
    annotation: string;
    status?: string;
    quota?: string;
    createdAt?: string;
}

interface Node {
    id: string;
    name: string;
    namespace: string;
    kind: string;
    group: number;
    x?: number;
    y?: number;
    vx?: number;
    vy?: number;
}

interface Link {
    source: string | Node;
    target: string | Node;
    value: number;
}

interface GraphData {
    nodes: Node[];
    links: Link[];
}

// Color scale function
const getGroupColor = (kind: string) => {
    const colors = {
        'Namespace': 'rgba(226, 144, 22, 0.8)',
        'Deployment': 'rgba(11, 138, 13, 0.8)',
        'Service': 'rgba(21, 103, 201, 0.8)',
        'Volume': 'rgba(154, 154, 149, 0.8)',
        'PersistentVolumeClaim': 'rgba(231, 5, 13, 0.8)',
        'DaemonSet': 'rgba(11, 199, 228, 0.8)',
        'Ingress': 'rgba(199, 11, 118, 0.8)',
        'Pod': 'rgba(14, 177, 98, 0.8)',
        'default': 'lightgray'
    };

    return colors[kind as keyof typeof colors] || colors['default'];
};

const KubernetesGraph: React.FC = () => {
    const [graphData, setGraphData] = useState<GraphData>({ nodes: [], links: [] });
    const [namespaces, setNamespaces] = useState<NamespaceData[]>([]);
    const [selectedNamespace, setSelectedNamespace] = useState<string>('all');
    const svgRef = useRef<SVGSVGElement>(null);
    const zoomRef = useRef<d3.ZoomBehavior<SVGSVGElement, unknown> | null>(null);
    const navigate = useNavigate();
    const nodeRadius = 30;
    const { theme } = useTheme();
    const isDarkMode = theme === 'dark';

    const fetchNamespaces = useCallback(async () => {
        try {
            const response = await axios_api.get('/k8s/namespaces');
            if (response.data && response.data.length > 0) {
                const nsData = response.data.map((ns: any) => ({
                    id: ns.name,
                    name: ns.name,
                    labels: ns.labels || '',
                    annotation: ns.annotation || '',
                    status: ns.status || '',
                    quota: ns.quota || '',
                    createdAt: ns.createdAt || ''
                }));
                setNamespaces(nsData);

                // Set first namespace as default if none selected
                if (!selectedNamespace && nsData.length > 0) {
                    setSelectedNamespace(nsData[0].name);
                }
            }
        } catch (err) {
        }
    }, [selectedNamespace]);

    const fetchKubernetesObjects = useCallback(async (namespace: string) => {
        if (!namespace) return;

        try {
            const response = await axios_api.get(`/k8s/objects?ns=${namespace === 'all' ? '' : namespace}`);
            const data: any[] = response.data;

            // Create nodes and links
            const nodes: Node[] = [];
            const links: Link[] = [];
            const processedLinks = new Set<string>();

            data.forEach(sourceObj => {
                const sourceId = `${sourceObj.kind}:${sourceObj.name}`;

                // Add node
                nodes.push({
                    id: sourceId,
                    name: sourceObj.name,
                    namespace: sourceObj.namespace,
                    kind: sourceObj.kind,
                    group: 0
                });

                // Add connections
                if (sourceObj.dependencies && sourceObj.dependencies.length > 0) {
                    sourceObj.dependencies.forEach((dep: string) => {
                        const match = dep.match(/(.*?)\s*\((.*?)\)/);
                        if (match) {
                            const depName = match[1].trim();
                            const depKind = match[2].trim();
                            const targetId = `${depKind}:${depName}`;
                            const linkKey = `${sourceId}->${targetId}`;

                            if (!processedLinks.has(linkKey)) {
                                // Check if target node exists
                                const targetExists = data.some(obj =>
                                    `${obj.kind}:${obj.name}` === targetId
                                );

                                if (targetExists) {
                                    links.push({
                                        source: sourceId,
                                        target: targetId,
                                        value: 1
                                    });
                                    processedLinks.add(linkKey);
                                }
                            }
                        }
                    });
                }
            });

            setGraphData({ nodes, links });
        } catch (err) {
        }
    }, []);

    useEffect(() => {
        fetchNamespaces();
    }, [fetchNamespaces]);

    useEffect(() => {
        if (selectedNamespace) {
            fetchKubernetesObjects(selectedNamespace);
        }
    }, [selectedNamespace, fetchKubernetesObjects]);

    // D3.js graph rendering
    useEffect(() => {
        if (!svgRef.current || graphData.nodes.length === 0) return;

        // Get SVG dimensions
        const width = svgRef.current.clientWidth;
        const height = svgRef.current.clientHeight;


        const tooltip = d3.select("body")
            .append("div")
            .attr("class", "tooltip")
            .style("position", "absolute")
            .style("padding", "12px 18px")
            .style("background", isDarkMode ? "#222222" : "#ffffff")
            .style("color", isDarkMode ? "#ffffff" : "#000000")
            .style("border", isDarkMode ? "2px solid #ffbf00" : "2px solid #ffbf00")  // dikkat çeken altın sarısı border
            .style("border-radius", "12px")
            .style("box-shadow", isDarkMode
                ? "0 6px 20px rgba(255, 191, 0, 0.8)"   // glow efekti koyu modda
                : "0 6px 20px rgba(255, 191, 0, 0.5)")  // glow efekti açık modda
            .style("pointer-events", "none")
            .style("font-family", "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif")
            .style("font-size", "14px")
            .style("font-weight", "600")
            .style("line-height", "1.5")
            .style("transition", "opacity 0.2s ease")
            .style("opacity", 0);

        // Clear previous content
        d3.select(svgRef.current).selectAll("*").remove();

        // Create SVG container with gradient background
        const svg = d3.select(svgRef.current)
            .attr("width", width)
            .attr("height", height);

        // Add gradient definition
        const defs = svg.append("defs");
        const gradient = defs.append("linearGradient")
            .attr("id", "graph-background")
            .attr("x1", "0%")
            .attr("y1", "0%")
            .attr("x2", "100%")
            .attr("y2", "100%");

        // Set colors based on theme
        if (isDarkMode) {
            gradient.append("stop")
                .attr("offset", "0%")
                .attr("stop-color", "#1a1d23");

            gradient.append("stop")
                .attr("offset", "100%")
                .attr("stop-color", "#111318");
        } else {
            gradient.append("stop")
                .attr("offset", "0%")
                .attr("stop-color", "#f5f7fa");

            gradient.append("stop")
                .attr("offset", "100%")
                .attr("stop-color", "#e4e8f0");
        }

        // Add background rectangle with gradient
        svg.append("rect")
            .attr("width", width)
            .attr("height", height)
            .attr("fill", "url(#graph-background)");

        // Add subtle grid pattern
        const gridSize = 20;
        const gridGroup = svg.append("g").attr("class", "grid");

        // Grid line color based on theme
        const gridColor = isDarkMode ? "rgba(255, 255, 255, 0.05)" : "#dde1e7";

        // Horizontal grid lines
        for (let y = 0; y < height; y += gridSize) {
            gridGroup.append("line")
                .attr("x1", 0)
                .attr("y1", y)
                .attr("x2", width)
                .attr("y2", y)
                .attr("stroke", gridColor)
                .attr("stroke-width", 0.5);
        }

        // Vertical grid lines
        for (let x = 0; x < width; x += gridSize) {
            gridGroup.append("line")
                .attr("x1", x)
                .attr("y1", 0)
                .attr("x2", x)
                .attr("y2", height)
                .attr("stroke", gridColor)
                .attr("stroke-width", 0.5);
        }

        // Create main container for graph elements
        const container = svg.append("g");

        // Create zoom behavior
        const zoomBehavior = d3.zoom<SVGSVGElement, unknown>()
            .scaleExtent([0.1, 4])
            .on("zoom", (event) => {
                container.attr("transform", event.transform);
            });

        // Save zoom behavior to ref
        zoomRef.current = zoomBehavior;

        // Add zoom behavior to SVG
        svg.call(zoomBehavior);

        // Add arrow marker for links
        svg.append('defs')
            .append('marker')
            .attr('id', 'arrowhead')
            .attr('viewBox', '-0 -5 10 10')
            .attr('refX', nodeRadius + 10)
            .attr('refY', 0)
            .attr('orient', 'auto')
            .attr('markerWidth', 7)
            .attr('markerHeight', 7)
            .attr('xoverflow', 'visible')
            .append('svg:path')
            .attr('d', 'M 0,-5 L 10 ,0 L 0,5')
            .attr('fill', isDarkMode ? '#777' : '#999')
            .style('stroke', 'none');

        // Create force simulation
        const simulation = d3.forceSimulation<Node, Link>()
            .force("link", d3.forceLink<Node, Link>()
                .id(d => d.id)
                .distance(100)
            )
            .force("charge", d3.forceManyBody().strength(-300))
            .force("center", d3.forceCenter(width / 2, height / 2))
            .force("collide", d3.forceCollide().radius(nodeRadius + 10));

        // Create links
        const link = container.append("g")
            .attr("stroke", isDarkMode ? "#777" : "#999")
            .attr("stroke-opacity", 0.6)
            .selectAll("line")
            .data(graphData.links)
            .join("line")
            .attr("stroke-width", d => Math.sqrt(d.value))
            .attr("marker-end", "url(#arrowhead)");

        // Create nodes
        const node = container.append("g")
            .selectAll("g")
            .data(graphData.nodes)
            .join("g")
            .call((g) => {
                d3.drag<SVGGElement, Node>()
                    .on("start", (event, d) => {
                        if (!event.active) simulation.alphaTarget(0.3).restart();
                        (d as any).fx = d.x;
                        (d as any).fy = d.y;
                    })
                    .on("drag", (event, d) => {
                        (d as any).fx = event.x;
                        (d as any).fy = event.y;
                    })
                    .on("end", (event, d) => {
                        if (!event.active) simulation.alphaTarget(0);
                        (d as any).fx = null;
                        (d as any).fy = null;
                    })(g as unknown as d3.Selection<SVGGElement, Node, null, undefined>);
            })
            .on("mouseover", (_, d) => {
                tooltip.transition().duration(200).style("opacity", 1);
                tooltip.html(`
        <strong>${d.name}</strong><br/>
        Kind: ${d.kind}<br/>
        Namespace: ${d.namespace}
    `);
            })
            .on("mousemove", (event) => {
                tooltip
                    .style("left", (event.pageX + 10) + "px")
                    .style("top", (event.pageY + 10) + "px");
            })
            .on("mouseout", () => {
                tooltip.transition().duration(200).style("opacity", 0);
            });

        // Node circles
        node.append("circle")
            .attr("r", nodeRadius)
            .attr("fill", d => getGroupColor(d.kind))
            .attr("stroke", "#fff")
            .attr("stroke-width", 1.5);

        // Node labels
        node.append("text")
            .attr("dy", ".35em")
            .attr("text-anchor", "middle")
            .text(d => d.name.length > 10 ? d.name.substring(0, 10) + "..." : d.name)
            .attr("fill", isDarkMode ? "#e4e8f0" : "#333")
            .style("font-size", "10px")
            .style("pointer-events", "none");


        // Simulation update function
        simulation.nodes(graphData.nodes)
            .on("tick", () => {
                link
                    .attr("x1", d => (d.source as Node).x!)
                    .attr("y1", d => (d.source as Node).y!)
                    .attr("x2", d => (d.target as Node).x!)
                    .attr("y2", d => (d.target as Node).y!);

                node.attr("transform", d => `translate(${d.x}, ${d.y})`);
            });

        (simulation.force("link") as d3.ForceLink<Node, Link>).links(graphData.links);

        // Center graph initially
        zoomBehavior.translateTo(svg, width / 2, height / 2);

        // Cleanup
        return () => {
            simulation.stop();
        };
    }, [graphData, navigate]);

    const handleZoomIn = () => {
        if (svgRef.current && zoomRef.current) {
            d3.select(svgRef.current).transition().call(zoomRef.current.scaleBy, 1.2);
        }
    };

    const handleZoomOut = () => {
        if (svgRef.current && zoomRef.current) {
            d3.select(svgRef.current).transition().call(zoomRef.current.scaleBy, 0.8);
        }
    };

    const handleFit = () => {
        if (svgRef.current && zoomRef.current) {
            const width = svgRef.current.clientWidth;
            const height = svgRef.current.clientHeight;

            d3.select(svgRef.current).transition().call(
                zoomRef.current.transform,
                d3.zoomIdentity.translate(width / 2, height / 2).scale(1)
            );
        }
    };

    // Legend items
    const legendItems = [
        { kind: 'Namespace', color: getGroupColor('Namespace') },
        { kind: 'Deployment', color: getGroupColor('Deployment') },
        { kind: 'Service', color: getGroupColor('Service') },
        { kind: 'Volume', color: getGroupColor('Volume') },
        { kind: 'PersistentVolumeClaim', color: getGroupColor('PersistentVolumeClaim') },
        { kind: 'DaemonSet', color: getGroupColor('DaemonSet') },
        { kind: 'Ingress', color: getGroupColor('Ingress') },
        { kind: 'Pod', color: getGroupColor('Pod') },
    ];

    // Show message if no data
    if (graphData.nodes.length === 0) {
        return (
            <>
                {/* ===== Top Heading ===== */}
                <Header fixed>
                    <Search />
                    <div className='ml-auto flex items-center space-x-4'>
                        <ThemeSwitch />
                        <ProfileDropdown />
                    </div>
                </Header>


                <Main fixed>
                    <div className="w-full h-screen flex items-center justify-center">
                        <div className="text-center p-8 bg-card rounded-lg shadow-md dark:border dark:border-border">
                            <h2 className="text-xl font-bold mb-4 text-foreground">No Kubernetes objects found</h2>
                            <p className="text-muted-foreground">Try selecting a different namespace or check your cluster connection.</p>
                            <div className="mt-4 flex justify-center">
                                <Select onValueChange={setSelectedNamespace} defaultValue={selectedNamespace}>
                                    <SelectTrigger className="w-[180px]">
                                        <SelectValue placeholder="Select Namespace" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {namespaces.map(ns => (
                                            <SelectItem key={ns.id} value={ns.name}>{ns.name}</SelectItem>
                                        ))}
                                        <SelectItem value="all">All</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </div>
                </Main>
            </>
        );
    }

    return (
        <>
            {/* ===== Top Heading ===== */}
            <Header fixed>
                <Search />
                <div className='ml-auto flex items-center space-x-4'>
                    <ThemeSwitch />
                    <ProfileDropdown />
                </div>
            </Header>


            <Main fixed>
                <div className="w-full h-screen flex flex-col">
                    {/* Control panel */}
                    <div className="p-4 bg-card border-b border-border flex justify-between items-center">
                        <div className="flex items-center space-x-2">
                            <label className="text-sm font-medium text-foreground">Namespace:</label>
                            <Select onValueChange={setSelectedNamespace} defaultValue={selectedNamespace}>
                                <SelectTrigger className="w-[180px]">
                                    <SelectValue placeholder="Select Namespace" />
                                </SelectTrigger>
                                <SelectContent>
                                    {namespaces.map(ns => (
                                        <SelectItem key={ns.id} value={ns.name}>{ns.name}</SelectItem>
                                    ))}
                                    <SelectItem value="all">All</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex items-center space-x-2">
                            <Button variant="outline" size="sm" onClick={handleZoomIn}>
                                <IconZoomIn size={16} className="mr-1" /> Zoom In
                            </Button>
                            <Button variant="outline" size="sm" onClick={handleZoomOut}>
                                <IconZoomOut size={16} className="mr-1" /> Zoom Out
                            </Button>
                            <Button variant="outline" size="sm" onClick={handleFit}>
                                <IconArrowsMaximize size={16} className="mr-1" /> Reset
                            </Button>
                        </div>
                        <div className="text-sm text-foreground">
                            <span className="font-medium">{graphData.nodes.length}</span> objects,
                            <span className="font-medium ml-1">{graphData.links.length}</span> connections
                        </div>
                    </div>

                    {/* Graph area */}
                    <div className="flex-1 overflow-hidden relative">
                        <svg
                            ref={svgRef}
                            className="w-full h-full"
                            style={{ cursor: 'grab' }}
                        ></svg>

                        {/* Legend */}
                        <div className="absolute bottom-4 right-4 bg-white p-3 rounded shadow-md">
                            <h4 className="text-sm font-bold mb-2">Legend</h4>
                            <ul className="space-y-1">
                                {legendItems.map((item, index) => (
                                    <li key={index} className="flex items-center">
                                        <span
                                            className="w-4 h-4 rounded-full mr-2"
                                            style={{ backgroundColor: item.color }}
                                        ></span>
                                        <span className="text-xs">{item.kind}</span>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    </div>
                </div>
            </Main>
        </>
    );
};

export default KubernetesGraph;
