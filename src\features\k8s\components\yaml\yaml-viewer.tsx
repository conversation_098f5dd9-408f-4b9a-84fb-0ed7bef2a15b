import { useState, useEffect, useRef } from 'react'
import axios_api from '@/lib/axios_api'
import { toast } from 'sonner'
import { IconCopy, IconFileCode, IconDeviceFloppy } from '@tabler/icons-react'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import {
  DropdownMenuItem,
  DropdownMenuShortcut,
} from '@/components/ui/dropdown-menu'
import Editor from "@monaco-editor/react"
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

interface YamlViewerProps {
  resourceName: string;
  resourceNamespace: string;
  resourceType: string;
  showYamlModal?: boolean;
  setShowYamlModal?: (show: boolean) => void;
}

export function YamlMenuItem({
  resourceName,
  resourceNamespace,
  resourceType,
  showYamlModal,
  setShowYamlModal
}: YamlViewerProps) {
  const editorRef = useRef<any>(null);
  const [localShowYamlModal, setLocalShowYamlModal] = useState(false)
  const [yamlContent, setYamlContent] = useState("")
  const [isEditable, setIsEditable] = useState(false)
  const [showConfirmApply, setShowConfirmApply] = useState(false)

  // Use provided state if available, otherwise use local state
  const isModalOpen = showYamlModal !== undefined ? showYamlModal : localShowYamlModal
  const setModalOpen = setShowYamlModal || setLocalShowYamlModal

  // Effect to fetch YAML when modal opens
  useEffect(() => {
    if (isModalOpen) {
      fetchYaml()
    }
  }, [isModalOpen])

  // Monaco Editor'ün dil ayarını yapmak için useEffect kullanmak yerine
  // doğrudan Editor bileşenine language prop'u geçiyoruz

  // Bu useEffect'i kaldırın:
  // useEffect(() => {
  //   if (monaco && editorRef.current) {
  //     const editor = editorRef.current;
  //     monaco.editor.setModelLanguage(editor.getModel(), 'yaml');
  //   }
  // }, [monaco, isModalOpen]);

  const handleEditorDidMount = (editor: any) => {
    editorRef.current = editor;
    // Burada dil ayarı yapmaya gerek yok, Editor bileşeni language prop'u ile yapıyor
  };

  const fetchYaml = async () => {
    setYamlContent("") // Clear content before fetching
    setIsEditable(false) // Reset edit mode

    const apiUrl = `/k8s/yaml/get?objectType=${resourceType}&objectName=${resourceName}${resourceNamespace ? `&namespace=${resourceNamespace}` : ''}`

    try {
      const response = await axios_api.get(apiUrl)
      setYamlContent(response.data)
    } catch (err) {
    }
  }

  const handleApply = async () => {
    try {
      const apiUrl = `/k8s/yaml/apply/${resourceType}/${resourceName}?namespace=${resourceNamespace || 'default'}`

      await axios_api.post(apiUrl, yamlContent, {
        headers: {
          "Content-Type": "application/yaml",
        },
      })

      toast.success("YAML successfully applied")
      setShowConfirmApply(false)
      setIsEditable(false)
      fetchYaml() // Refresh content
    } catch (err) {
    }
  }


  const copyToClipboard = (content: string) => {
    navigator.clipboard.writeText(content)
      .then(() => toast.success("YAML copied to clipboard"))
      .catch(() => toast.error("Failed to copy YAML"))
  }

  // Handle dialog open/close with controlled state
  const handleOpenChange = (open: boolean) => {
    setModalOpen(open)
    // Clear content when closing
    if (!open) {
      setYamlContent("")
      setIsEditable(false)
    }
  }

  // If we're using external state management, don't render the menu item
  if (showYamlModal !== undefined && setShowYamlModal !== undefined) {
    return (
      <>
        <Dialog open={isModalOpen} onOpenChange={handleOpenChange}>
          <DialogContent className="max-w-[80vw] w-[1200px]">
            <DialogHeader>
              <DialogTitle className="flex items-center justify-between">
                <span>{resourceType}/{resourceName}</span>
              </DialogTitle>
              <DialogDescription>
                YAML representation of the {resourceType} resource
              </DialogDescription>
            </DialogHeader>
            <div className="h-[400px] w-full">
              <Editor
                width="100%"
                height="100%"
                language="yaml" // Bu prop ile dil ayarı yapılıyor
                value={yamlContent}
                theme="vs-dark"
                options={{
                  selectOnLineNumbers: true,
                  roundedSelection: false,
                  readOnly: !isEditable,
                  cursorStyle: 'line',
                  automaticLayout: true,
                  wordWrap: 'on',
                  scrollBeyondLastLine: false,
                }}
                onChange={(value) => isEditable && setYamlContent(value || "")}
                onMount={handleEditorDidMount}
              />
            </div>
            <DialogFooter className="flex justify-between">

              <div className="flex items-center space-x-2">
                <Switch
                  id="edit-mode"
                  checked={isEditable}
                  onCheckedChange={setIsEditable}
                />
                <Label htmlFor="edit-mode">{isEditable ? "Edit Mode" : "View Mode"}</Label>
              </div>

              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(yamlContent)}
                >
                  <IconCopy size={16} className="mr-1" />
                  Copy
                </Button>
                {isEditable && (
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => setShowConfirmApply(true)}
                  >
                    <IconDeviceFloppy size={16} className="mr-1" />
                    Apply
                  </Button>
                )}
              </div>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Confirm Apply Dialog */}
        <AlertDialog open={showConfirmApply} onOpenChange={setShowConfirmApply}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Apply Changes</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to apply these changes? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleApply}>Apply</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>


      </>
    )
  }

  return (
    <>
      <DropdownMenuItem onClick={() => setModalOpen(true)}>
        Show YAML
        <DropdownMenuShortcut>
          <IconFileCode size={16} />
        </DropdownMenuShortcut>
      </DropdownMenuItem>

      <Dialog open={isModalOpen} onOpenChange={handleOpenChange}>
        <DialogContent className="max-w-[80vw] w-[1200px]">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>{resourceType}/{resourceName}</span>

            </DialogTitle>
            <DialogDescription>
              YAML representation of the {resourceType} resource
            </DialogDescription>
          </DialogHeader>
          <div className="h-[400px] w-full">
            <Editor
              width="100%"
              height="100%"
              language="yaml" // Bu prop ile dil ayarı yapılıyor
              value={yamlContent}
              theme="vs-dark"
              options={{
                selectOnLineNumbers: true,
                roundedSelection: false,
                readOnly: !isEditable,
                cursorStyle: 'line',
                automaticLayout: true,
                wordWrap: 'on',
                scrollBeyondLastLine: false,
              }}
              onChange={(value) => isEditable && setYamlContent(value || "")}
              onMount={handleEditorDidMount}
            />
          </div>
          <DialogFooter className="flex justify-between">

            <div className="flex items-center space-x-2">
              <Switch
                id="edit-mode"
                checked={isEditable}
                onCheckedChange={setIsEditable}
              />
              <Label htmlFor="edit-mode">{isEditable ? "Edit Mode" : "View Mode"}</Label>
            </div>

            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(yamlContent)}
              >
                <IconCopy size={16} className="mr-1" />
                Copy
              </Button>
              {isEditable && (
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => setShowConfirmApply(true)}
                >
                  <IconDeviceFloppy size={16} className="mr-1" />
                  Apply
                </Button>
              )}
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Confirm Apply Dialog */}
      <AlertDialog open={showConfirmApply} onOpenChange={setShowConfirmApply}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Apply Changes</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to apply these changes? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleApply}>Apply</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>


    </>
  )
}






