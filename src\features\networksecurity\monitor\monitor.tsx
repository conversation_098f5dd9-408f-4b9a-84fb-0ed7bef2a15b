// monitor.tsx
import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { CheckedState } from "@radix-ui/react-checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { X as DeleteIcon, Settings } from 'lucide-react';
import axios_api from '@/lib/axios_api';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { DataTable } from '../component/data-table';
import { columns } from './component/columns';

interface MonitorTabProps {
    data: any[];
    loading: boolean;
    setData: (data: any[]) => void;
    setLoading: (loading: boolean) => void;
}

export function MonitorTab({ data, loading, setData, setLoading }: MonitorTabProps) {
    const [fromDate, setFromDate] = useState('');
    const [toDate, setToDate] = useState('');
    const [tcpOnly, setTcpOnly] = useState(false);
    const [includeDNS, setIncludeDNS] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // Define applied filters that will be used for the actual API request
    const [appliedFilters, setAppliedFilters] = useState({
        sourceIP: '',
        destinationIP: '',
        sourcePort: '',
        destinationPort: '',
        protocol: '',
        protocols: '',
        excludePorts: '',
        sourcePodName: '',
        destinationPodName: '',
        sourcePodNamespace: '',
        destinationPodNamespace: '',
    });

    const [refreshInterval, setRefreshInterval] = useState('stop');
    const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null);

    // Handle TCP Only flag change
    const handleTcpOnlyFlagChange = (checked: CheckedState) => {
        setTcpOnly(checked === true);
        if (checked === true) {
            setAppliedFilters((prev) => ({
                ...prev,
                protocols: 'TCP'
            }));
        } else {
            const { protocols: _protocols, ...filtersWithoutProtocols } = appliedFilters;
            setAppliedFilters({
                ...filtersWithoutProtocols,
                protocols: ''
            });
        }
    };

    // Handle Include DNS flag change
    const handleIncludeDNSFlagChange = (checked: CheckedState) => {
        setIncludeDNS(checked === true);
        if (checked === true) {
            const { excludePorts: _excludePorts, ...filtersWithoutExcludePort } = appliedFilters;
            setAppliedFilters({
                ...filtersWithoutExcludePort,
                excludePorts: ''
            });
        } else {
            setAppliedFilters((prev) => ({
                ...prev,
                excludePorts: '53',
            }));
        }
    };

    // Handle adding a filter when a cell is clicked
    const handleCellClick = (columnId: string, value: string) => {
        if (!value) return;

        // Map the column ID to the corresponding filter key
        const filterMap: Record<string, string> = {
            'sourceIp': 'sourceIP',
            'destinationIp': 'destinationIP',
            'sourcePort': 'sourcePort',
            'destinationPort': 'destinationPort',
            'protocol': 'protocol',
            'sourcePodName': 'sourcePodName',
            'destinationPodName': 'destinationPodName',
            'sourcePodNamespace': 'sourcePodNamespace',
            'destinationPodNamespace': 'destinationPodNamespace',
        };

        const filterKey = filterMap[columnId];
        if (filterKey) {
            const newFilters = {
                ...appliedFilters,
                [filterKey]: value
            };

            setAppliedFilters(newFilters);

            // Immediately apply the filter
            fetchData(newFilters);
        }
    };

    const handleIntervalChange = (value: string) => {
        if (intervalId) {
            clearInterval(intervalId);
            setIntervalId(null);
        }

        setRefreshInterval(value);

        if (value !== 'stop') {
            const seconds = parseInt(value);
            const id = setInterval(() => {
                fetchData(appliedFilters);
            }, seconds * 1000);
            setIntervalId(id);
        }
    };

    // Updated fetchData function that accepts filters as a parameter
    const fetchData = useCallback(async (filters = appliedFilters) => {
        setLoading(true);
        try {
            const params = new URLSearchParams();

            // Add filter parameters
            for (const [key, value] of Object.entries(filters)) {
                if (value !== '') {
                    params.append(key, value);
                }
            }

            // Add date range parameters
            if (fromDate) params.append("fromDate", fromDate);
            if (toDate) params.append("toDate", toDate);

            // Build the URL with query parameters
            const url = `/k8s/search_network_packets${params.toString() ? '?' + params.toString() : ''}`;

            // Make the API request
            const response = await axios_api.get(url);
            const responseData = response.data;

            // Update the data state
            setData(responseData);
            setError(null);
        } catch (error: any) {
            setError(error.message || "An unexpected error occurred");
        } finally {
            setLoading(false);
        }
    }, [fromDate, toDate, appliedFilters, setData, setLoading]);

    const applyFilters = () => {
        fetchData(appliedFilters);
    };

    const clearFilters = () => {
        setFromDate('');
        setToDate('');
        setTcpOnly(false);
        setIncludeDNS(true);
        const emptyFilters = {
            sourceIP: '',
            destinationIP: '',
            sourcePort: '',
            destinationPort: '',
            protocol: '',
            protocols: '',
            excludePorts: '',
            sourcePodName: '',
            destinationPodName: '',
            sourcePodNamespace: '',
            destinationPodNamespace: '',
        };
        setAppliedFilters(emptyFilters);
        fetchData(emptyFilters);
    };

    // Remove a specific filter
    const handleRemoveFilter = (key: string) => {
        setAppliedFilters(prev => ({
            ...prev,
            [key]: ''
        }));
    };

    useEffect(() => {
        fetchData();

        return () => {
            if (intervalId) {
                clearInterval(intervalId);
            }
        };
    }, [fetchData]);

    return (
        <Card>
            <CardContent className="pt-6">
                <div className="grid grid-cols-1 md:grid-cols-1 gap-4 mb-4">
                    {/* Filters */}
                    <div className="bg-card p-4 rounded-lg border shadow-sm">
                        <div className="flex justify-between items-center mb-3">
                            <h2 className="text-lg font-semibold">Filters</h2>

                            <div className="flex items-center space-x-2">
                                {/* Refresh Interval Selector */}
                                <Select value={refreshInterval} onValueChange={handleIntervalChange}>
                                    <SelectTrigger className="w-28 h-8 text-xs">
                                        <SelectValue placeholder="Refresh" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="stop">Stop</SelectItem>
                                        <SelectItem value="15">15s</SelectItem>
                                        <SelectItem value="30">30s</SelectItem>
                                        <SelectItem value="45">45s</SelectItem>
                                        <SelectItem value="60">60s</SelectItem>
                                    </SelectContent>
                                </Select>

                                {/* Internal Filters Icon */}
                                <Popover>
                                    <PopoverTrigger asChild>
                                        <Button variant="outline" size="icon" className="h-7 w-7">
                                            <Settings className="h-4 w-4" />
                                        </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-72 p-3">
                                        <div className="space-y-2">
                                            {Object.entries(appliedFilters).map(([key, value]) => (
                                                <div key={key} className="flex items-center gap-1">
                                                    <label
                                                        htmlFor={`filter-${key}`}
                                                        className="text-xs font-medium text-gray-700 w-20 truncate" // Smaller label, fixed width, truncate
                                                    >
                                                        {key}:
                                                    </label>
                                                    <Input
                                                        type="text"
                                                        id={`filter-${key}`}
                                                        placeholder="Value" // Simpler placeholder
                                                        value={value}
                                                        onChange={(e) => {
                                                            setAppliedFilters((prev) => ({ ...prev, [key]: e.target.value }));
                                                        }}
                                                        className="flex-1 text-xs h-8" // Smaller input
                                                    />
                                                    <Button
                                                        variant="ghost"
                                                        size="icon"
                                                        className="h-6 w-6 p-1" // Smaller button
                                                        onClick={() => handleRemoveFilter(key)}
                                                        aria-label={`Remove ${key} filter`}
                                                    >
                                                        <DeleteIcon className="h-3 w-3" />
                                                    </Button>
                                                </div>
                                            ))}
                                        </div>
                                    </PopoverContent>
                                </Popover>
                            </div>
                        </div>

                        {/* Active filters display */}
                        {Object.entries(appliedFilters).some(([_, value]) => value !== '') && (
                            <div className="flex flex-wrap items-center gap-2 mb-3 p-2 rounded-md bg-muted">
                                <span className="text-sm font-medium text-muted-foreground mr-1">
                                    Applied Filters:
                                </span>
                                {Object.entries(appliedFilters)
                                    .filter(([_, value]) => value) // Only process filters with values
                                    .map(([key, value]) => (
                                        <div
                                            key={key}
                                            className="inline-flex items-center rounded-full px-3 py-1 text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-secondary text-secondary-foreground"
                                        >
                                            <span className="mr-2">{key}: {value}</span>
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                className="h-5 w-5 -mr-1"
                                                onClick={() => handleRemoveFilter(key)}
                                                aria-label={`Remove filter for ${key}`}
                                            >
                                                <DeleteIcon className="h-3 w-3" />
                                            </Button>
                                        </div>
                                    ))}
                            </div>
                        )}

                        <div className="grid grid-cols-1 md:grid-cols-4 gap-2 mb-3">
                            <div className="md:col-span-1">
                                <Input
                                    id="fromDate"
                                    type="datetime-local"
                                    value={fromDate}
                                    onChange={(e) => setFromDate(e.target.value)}
                                    className="h-8 text-xs"
                                    placeholder="From Date"
                                />
                            </div>

                            <div className="md:col-span-1">
                                <Input
                                    id="toDate"
                                    type="datetime-local"
                                    value={toDate}
                                    onChange={(e) => setToDate(e.target.value)}
                                    className="h-8 text-xs"
                                    placeholder="To Date"
                                />
                            </div>

                            <div className="md:col-span-1 flex items-center space-x-2">
                                <div className="flex items-center">
                                    <Checkbox
                                        id="tcpOnlyCheckBox"
                                        checked={tcpOnly}
                                        onCheckedChange={handleTcpOnlyFlagChange}
                                        className="h-3.5 w-3.5"
                                    />
                                    <label htmlFor="tcpOnlyCheckBox" className="text-xs font-medium ml-1.5">
                                        TCP Only
                                    </label>
                                </div>

                                <div className="flex items-center ml-3">
                                    <Checkbox
                                        id="includeDNSCheckBox"
                                        checked={includeDNS}
                                        onCheckedChange={handleIncludeDNSFlagChange}
                                        className="h-3.5 w-3.5"
                                    />
                                    <label htmlFor="includeDNSCheckBox" className="text-xs font-medium ml-1.5">
                                        Include DNS
                                    </label>
                                </div>
                            </div>

                            <div className="md:col-span-1 flex gap-2">
                                <Button onClick={applyFilters} size="sm" className="h-8 text-xs flex-1">
                                    Apply
                                </Button>
                                <Button onClick={clearFilters} variant="outline" size="sm" className="h-8 text-xs flex-1">
                                    Clear
                                </Button>
                            </div>
                        </div>

                        {error && (
                            <div className="bg-destructive/15 text-destructive p-2 rounded-md text-xs">
                                {error}
                            </div>
                        )}
                    </div>
                </div>

                {/* DataTable with full width and proper spacing */}
                <div className="w-full">
                    <DataTable
                        columns={columns}
                        data={data}
                        loading={loading}
                        onCellClick={handleCellClick}
                    />
                </div>
            </CardContent>
        </Card>
    );
}
