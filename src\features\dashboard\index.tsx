import { But<PERSON> } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Overview } from './components/overview'
import { Dashboard_Index } from './components/dashboard_index'
import { useEffect, useState } from 'react'
import axios_api from '@/lib/axios_api'
import ReportK8S from '../report'
import { saveAs } from 'file-saver'
import { Badge } from '@/components/ui/badge'
import { Download } from 'lucide-react'

export default function Dashboard() {
  const [resourceCounts, setResourceCounts] = useState({
    nodes: 0,
    namespaces: 0,
    deployments: 0,
    pods: 0
  })
  const [masterUrl, setMasterUrl] = useState("")

  useEffect(() => {
    async function fetchResourceCounts() {
      try {
        const response = await axios_api.get("/k8s/counts")
        const { nodes, namespaces, deployments, pods } = response.data
        setResourceCounts({ nodes, namespaces, deployments, pods })
      } catch (err) {
      }
    }

    async function fetchMasterInfo() {
      try {
        const response = await axios_api.get("/k8s/master-info")
        setMasterUrl(response.data.masterUrl)
      } catch (err) {
        console.error("Failed to fetch master info:", err)
      }
    }

    fetchResourceCounts()
    fetchMasterInfo()
  }, [])

  const downloadKubeConfig = () => {
    axios_api
      .get("/k8s/download-config", { responseType: "blob" })
      .then((res) => saveAs(res.data, "kubeconfig"))
      .catch((err) => console.error("Download failed", err));
  };

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-2 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>Dashboard</h1>
          <div className='flex flex-col items-end space-y-2'>
            <Card className="w-full max-w-sm p-4 border shadow-md rounded-2xl">
              <CardContent className="flex items-center justify-between p-0">
                <Button
                  onClick={downloadKubeConfig}
                  className="w-8 h-8 p-0"
                  variant="outline"
                  size="icon"
                >
                  <Download className="w-6 h-6" />
                </Button>
                {masterUrl && (
                  <Badge variant="outline" className="text-xs font-medium ">
                    Connected:
                    <a href={masterUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 underline">
                      {masterUrl}
                    </a>
                  </Badge>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
        <Tabs
          orientation='vertical'
          defaultValue='overview'
          className='space-y-4'
        >
          <div className='w-full overflow-x-auto pb-2'>
            <TabsList>
              <TabsTrigger value='overview'>Overview</TabsTrigger>
              <TabsTrigger value='reports'>Reports</TabsTrigger>
            </TabsList>
          </div>
          <TabsContent value='overview' className='space-y-4'>
            <div className='grid gap-4 sm:grid-cols-2 lg:grid-cols-4'>
              <Card>
                <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                  <CardTitle className='text-sm font-medium'>
                    Nodes
                  </CardTitle>
                  <svg
                    xmlns='http://www.w3.org/2000/svg'
                    viewBox='0 0 24 24'
                    fill='none'
                    stroke='currentColor'
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth='2'
                    className='text-muted-foreground h-4 w-4'
                  >
                    <path d='M21 10H3M21 6H3M21 14H3M21 18H3' />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className='text-2xl font-bold'>{resourceCounts.nodes}</div>
                  <p className='text-muted-foreground text-xs'>
                    Kubernetes Nodes
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                  <CardTitle className='text-sm font-medium'>
                    Namespaces
                  </CardTitle>
                  <svg
                    xmlns='http://www.w3.org/2000/svg'
                    viewBox='0 0 24 24'
                    fill='none'
                    stroke='currentColor'
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth='2'
                    className='text-muted-foreground h-4 w-4'
                  >
                    <path d='M12 2a10 10 0 1 0 10 10H12V2z' />
                    <path d='M20 12a8 8 0 1 0-16 0' />
                    <path d='M2 12h10v10' />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className='text-2xl font-bold'>{resourceCounts.namespaces}</div>
                  <p className='text-muted-foreground text-xs'>
                    Kubernetes Namespaces
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                  <CardTitle className='text-sm font-medium'>Deployments</CardTitle>
                  <svg
                    xmlns='http://www.w3.org/2000/svg'
                    viewBox='0 0 24 24'
                    fill='none'
                    stroke='currentColor'
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth='2'
                    className='text-muted-foreground h-4 w-4'
                  >
                    <path d='M8 18L12 22L16 18' />
                    <path d='M12 2V22' />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className='text-2xl font-bold'>{resourceCounts.deployments}</div>
                  <p className='text-muted-foreground text-xs'>
                    Kubernetes Deployments
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                  <CardTitle className='text-sm font-medium'>
                    Pods
                  </CardTitle>
                  <svg
                    xmlns='http://www.w3.org/2000/svg'
                    viewBox='0 0 24 24'
                    fill='none'
                    stroke='currentColor'
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth='2'
                    className='text-muted-foreground h-4 w-4'
                  >
                    <path d='M12 2L2 7l10 5 10-5-10-5z' />
                    <path d='M2 17l10 5 10-5' />
                    <path d='M2 12l10 5 10-5' />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className='text-2xl font-bold'>{resourceCounts.pods}</div>
                  <p className='text-muted-foreground text-xs'>
                    Kubernetes Pods
                  </p>
                </CardContent>
              </Card>
            </div>
            <div className='grid grid-cols-1 gap-4 lg:grid-cols-7'>
              <Card className='col-span-1 lg:col-span-4'>
                <CardHeader>
                  <CardTitle>Overview</CardTitle>
                </CardHeader>
                <CardContent className='pl-2'>
                  <Overview />
                </CardContent>
              </Card>
              <Card className='col-span-1 lg:col-span-3'>
                <CardHeader>
                  <CardTitle>Kubernetes Events</CardTitle>
                  <CardDescription>
                    View recent Kubernetes events
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Dashboard_Index />
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          <TabsContent value='reports' className='space-y-4'>
            <ReportK8S></ReportK8S>
          </TabsContent>
        </Tabs>
      </Main>
    </>
  )
}

const topNav = [
  {
    title: 'Overview',
    href: '/',
    isActive: true,
    disabled: false,
  },
  {
    title: 'Kubernetes',
    href: '/k8s',
    isActive: true,
    disabled: false,
  },
  {
    title: 'AI',
    href: '/chats',
    isActive: true,
    disabled: false,
  },
  {
    title: 'Settings',
    href: '/settings',
    isActive: true,
    disabled: false,
  },
]
