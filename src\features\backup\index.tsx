import { useEffect, useState, useMemo, useRef } from "react";
import axios_api from "@/lib/axios_api";
import {
  Card
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  IconFolder,
  IconFile,
  IconSearch,
  IconChevronDown,
  IconChevronUp,
  IconCalendar,
  IconDownload,
  IconCopy,
  IconX,
} from "@tabler/icons-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { saveAs } from "file-saver";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import Editor from "@monaco-editor/react";
import { Header } from "@/components/layout/header";
import { Main } from "@/components/layout/main";
import { Search } from "@/components/search";
import { ThemeSwitch } from "@/components/theme-switch";
import { ProfileDropdown } from "@/components/profile-dropdown";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Download } from "lucide-react";

interface NamespaceBackup {
  namespace: string;
  files: string[];
}

function BackupExplorer() {
  const [timestamps, setTimestamps] = useState<string[]>([]);
  const [selectedTimestamp, setSelectedTimestamp] = useState<string | null>(null);
  const [namespaces, setNamespaces] = useState<NamespaceBackup[]>([]);
  const [expandedNamespace, setExpandedNamespace] = useState<string | null>(null);
  const [namespaceFilter, setNamespaceFilter] = useState("");
  const [fileSearch, setFileSearch] = useState("");
  const [isDownloading, setIsDownloading] = useState(false);
  const [showFileDialog, setShowFileDialog] = useState(false);
  const [selectedFile, setSelectedFile] = useState<{ namespace: string; file: string; content: string } | null>(null);
  const editorRef = useRef<any>(null);

  const handleEditorDidMount = (editor: any) => {
    editorRef.current = editor;
  };

  useEffect(() => {
    axios_api.get("/k8s/backups")
      .then(response => setTimestamps(response.data))
      .catch(error => {
        console.error("Error loading backups:", error);
        toast.error("Error loading backups");
      });
  }, []);

  const selectTimestamp = (ts: string) => {
    setSelectedTimestamp(ts);
    setExpandedNamespace(null);
    setNamespaceFilter("");
    setFileSearch("");
    axios_api.get(`/k8s/backups/${ts}`)
      .then(response => setNamespaces(response.data))
      .catch(() => { });
  };

  const toggleNamespace = (ns: string) => {
    setExpandedNamespace(expandedNamespace === ns ? null : ns);
  };

  const loadFileContent = async (ns: string, file: string) => {
    try {
      const response = await axios_api.get(`/k8s/backups/${selectedTimestamp}/${ns}/${file}`);
      const content = response.data;
      setSelectedFile({ namespace: ns, file, content });
      setShowFileDialog(true);
    } catch (error) { }
  };

  const downloadBackupAsZip = async () => {
    if (!selectedTimestamp) return;

    setIsDownloading(true);
    try {
      const response = await axios_api.get(`/k8s/backups/${selectedTimestamp}/zip`, {
        responseType: 'blob'
      });

      const fileName = `backup_${selectedTimestamp}.zip`;
      saveAs(new Blob([response.data]), fileName);
      toast.success("Backup downloaded successfully");
    } catch (error) {
      toast.error("Failed to download backup");
    } finally {
      setIsDownloading(false);
    }
  };

  const copyToClipboard = () => {
    if (selectedFile) {
      navigator.clipboard.writeText(selectedFile.content)
        .then(() => toast.success("Content copied to clipboard"))
        .catch(() => toast.error("Failed to copy"));
    }
  };

  const getLanguageFromFileName = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();

    switch (extension) {
      case 'yaml':
      case 'yml':
        return 'yaml';
      case 'json':
        return 'json';
      case 'xml':
        return 'xml';
      case 'sh':
        return 'shell';
      case 'js':
        return 'javascript';
      case 'py':
        return 'python';
      case 'java':
        return 'java';
      case 'go':
        return 'go';
      case 'rs':
        return 'rust';
      case 'ts':
      case 'tsx':
        return 'typescript';
      case 'jsx':
        return 'javascript';
      case 'md':
        return 'markdown';
      case 'txt':
      default:
        if (selectedFile?.content.trim().startsWith('{') || selectedFile?.content.trim().startsWith('[')) {
          return 'json';
        }
        if (selectedFile?.content.includes(':') && !selectedFile?.content.includes('{')) {
          return 'yaml';
        }
        return 'plaintext';
    }
  };

  const filteredNamespaces = useMemo(() => {
    return namespaces
      .filter(ns => ns.namespace.toLowerCase().includes(namespaceFilter.toLowerCase()))
      .map(ns => {
        if (!fileSearch.trim()) return ns;
        const filteredFiles = ns.files.filter(f => f.toLowerCase().includes(fileSearch.toLowerCase()));
        return { ...ns, files: filteredFiles };
      })
      .filter(ns => ns.files.length > 0);
  }, [namespaces, namespaceFilter, fileSearch]);

  const clearFilters = () => {
    setNamespaceFilter("");
    setFileSearch("");
  };

  const hasActiveFilters = namespaceFilter || fileSearch;

  const downloadContent = () => {
    if (!selectedFile) return;
    const blob = new Blob([selectedFile.content], {
      type: "text/plain;charset=utf-8",
    });
    saveAs(blob, selectedFile.file);
  };

  return (
    <>
      <Header fixed>
        <Search />
        <div className="ml-auto flex items-center space-x-4">
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>
      <Main>
        <div className="container max-w-6xl">
          <div className="flex flex-col space-y-4">
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-bold flex items-center gap-2">
                <IconCalendar className="text-primary" size={24} />
                Kubernetes Backup Explorer
              </h1>

              <div className="flex items-center gap-2">
                {selectedTimestamp && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={downloadBackupAsZip}
                          disabled={isDownloading}
                          className="flex items-center gap-1"
                        >
                          <IconDownload size={16} />
                          {isDownloading ? "Downloading..." : "Download"}
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Download entire backup as ZIP</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>
            </div>

            <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
              <div className="w-full md:w-64">
                <Select value={selectedTimestamp || ""} onValueChange={selectTimestamp}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select backup timestamp" />
                  </SelectTrigger>
                  <SelectContent>
                    {timestamps.map(ts => (
                      <SelectItem key={ts} value={ts}>
                        {ts}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex-1 flex flex-col md:flex-row gap-2 items-center">
                <div className="relative w-full md:w-auto flex-1">
                  <IconSearch className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" size={16} />
                  <Input
                    placeholder="Search namespace..."
                    className="pl-9 pr-8"
                    value={namespaceFilter}
                    onChange={e => setNamespaceFilter(e.target.value)}
                  />
                  {namespaceFilter && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6"
                      onClick={() => setNamespaceFilter("")}
                    >
                      <IconX size={14} />
                    </Button>
                  )}
                </div>

                <div className="relative w-full md:w-auto flex-1">
                  <IconSearch className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" size={16} />
                  <Input
                    placeholder="Search file name..."
                    className="pl-9 pr-8"
                    value={fileSearch}
                    onChange={e => setFileSearch(e.target.value)}
                  />
                  {fileSearch && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6"
                      onClick={() => setFileSearch("")}
                    >
                      <IconX size={14} />
                    </Button>
                  )}
                </div>

                {hasActiveFilters && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearFilters}
                    className="w-full md:w-auto"
                  >
                    Clear Filters
                  </Button>
                )}
              </div>
            </div>

            {selectedTimestamp ? (
              <>
                {filteredNamespaces.length === 0 ? (
                  <Card className="p-8 text-center text-muted-foreground">
                    <div className="flex flex-col items-center gap-2">
                      <IconSearch size={48} className="opacity-20" />
                      <p>No matching results found.</p>
                      {hasActiveFilters && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={clearFilters}
                          className="mt-2"
                        >
                          Clear Filters
                        </Button>
                      )}
                    </div>
                  </Card>
                ) : (
                  <div className="grid gap-3">
                    {filteredNamespaces.map(ns => (
                      <Card key={ns.namespace} className="overflow-hidden border shadow-md hover:shadow-lg transition-shadow duration-200">
                        <Button
                          variant="ghost"
                          onClick={() => toggleNamespace(ns.namespace)}
                          className="w-full flex justify-between items-center px-4 py-3 h-auto rounded-none hover:bg-muted/50"
                        >
                          <div className="flex items-center gap-2 font-medium">
                            <IconFolder className="text-primary" size={18} />
                            <span className="truncate max-w-[200px] md:max-w-md">{ns.namespace}</span>
                            <Badge variant="outline" className="ml-1">
                              {ns.files.length}
                            </Badge>
                          </div>
                          {expandedNamespace === ns.namespace ?
                            <IconChevronUp size={18} className="text-muted-foreground" /> :
                            <IconChevronDown size={18} className="text-muted-foreground" />
                          }
                        </Button>

                        {expandedNamespace === ns.namespace && (
                          <div className="border-t">
                            <ScrollArea className="h-[250px]">
                              <div className="divide-y">
                                {ns.files.map((file) => (
                                  <div key={file} className="flex items-center justify-between p-2.5 hover:bg-muted/30">
                                    <div className="flex items-center gap-2 truncate flex-1">
                                      <IconFile size={16} className="text-muted-foreground flex-shrink-0" />
                                      <span className="truncate text-sm">{file}</span>
                                    </div>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => loadFileContent(ns.namespace, file)}
                                      className="ml-2 flex-shrink-0"
                                    >
                                      View
                                    </Button>
                                  </div>
                                ))}
                              </div>
                            </ScrollArea>
                          </div>
                        )}
                      </Card>
                    ))}
                  </div>
                )}
              </>
            ) : (
              <Card className="p-8 text-center shadow-md hover:shadow-lg transition-all duration-200 bg-card/50">
                <div className="flex flex-col items-center gap-4">
                  <IconCalendar size={48} className="text-primary opacity-50" />
                  <div>
                    <h3 className="text-lg font-medium mb-1">Select a Backup Timestamp</h3>
                    <p className="text-muted-foreground">Choose a timestamp to explore Kubernetes backups</p>
                  </div>
                </div>
              </Card>
            )}
          </div>
        </div>
      </Main>

      <Dialog open={showFileDialog} onOpenChange={setShowFileDialog}>
        <DialogContent className="w-[80vw] h-[80vh] max-w-none max-h-none p-4 flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <IconFile size={18} className="text-primary" />
              <span className="truncate">
                {selectedFile?.namespace}/{selectedFile?.file}
              </span>
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 w-full border rounded-md overflow-hidden">
            {selectedFile && (
              <Editor
                height="100%"
                width="100%"
                language={getLanguageFromFileName(selectedFile.file)}
                value={selectedFile.content}
                theme="vs-dark"
                options={{
                  readOnly: true,
                  minimap: { enabled: true },
                  scrollBeyondLastLine: false,
                  wordWrap: 'on',
                  automaticLayout: true,
                  fontSize: 13,
                  lineNumbers: 'on',
                  renderLineHighlight: 'all',
                  formatOnPaste: true,
                }}
                onMount={handleEditorDidMount}
              />
            )}
          </div>

          <DialogFooter className="pt-4">
            <Button
              variant="outline"
              size="sm"
              onClick={copyToClipboard}
              className="flex items-center gap-1"
            >
              <IconCopy size={16} />
              Copy to Clipboard
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={downloadContent} // ← indirme fonksiyonunu burada çağır
              className="flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              Download
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={() => setShowFileDialog(false)}
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

    </>
  );
}

export default BackupExplorer;
