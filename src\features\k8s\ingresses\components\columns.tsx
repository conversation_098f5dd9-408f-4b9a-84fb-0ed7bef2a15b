import { Checkbox } from '@/components/ui/checkbox'
import { ColumnDef } from '@tanstack/react-table';
import { DataTableRowActions } from './data-table-row-actions';
import { DataTableColumnHeader } from '../../components/data-table-column-header';
import { formatDateToLocal } from '@/lib/dateformat';



interface DataEntity {
  id: string;
  name: string;
  namespace: string;
  rulesAndPaths: string;
  type: string;
  className: string;
  createdAt: string;
  labels?: Record<string, string>;
  annotation?: Record<string, string>;
  lastSeenAt: string;
}


export const columns: ColumnDef<DataEntity>[] = [
   {
    id: 'select',
    header: () => null, // Remove header checkbox for multiple selection
    cell: ({ row, table }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => {
          // Deselect all rows first
          table.toggleAllRowsSelected(false)
          // Then select only this row
          row.toggleSelected(!!value)
        }}
        aria-label='Select row'
        className='translate-y-[2px]'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Name' />,
    cell: ({ row }) => <div >{row.getValue('name')}</div>,
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'namespace',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Namespace' />,
    cell: ({ row }) => <div >{row.getValue('namespace')}</div>,
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'rulesAndPaths',
    header: ({ column }) => <DataTableColumnHeader column={column} title="RulesAndPaths" />,
    cell: ({ row }) => {
      const value = row.getValue<string>('rulesAndPaths');
      const urls = value.split(',').map((url, index) => (
        <div key={index}>
          <a href={url} target="_blank" rel="noopener noreferrer" className="text-blue-600 underline">
            {url}
          </a>
        </div>
      ));
      return <div>{urls}</div>;
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'type',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Type' />,
    cell: ({ row }) => <div >{row.getValue('type')}</div>,
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'className',
    header: ({ column }) => <DataTableColumnHeader column={column} title='ClassName' />,
    cell: ({ row }) => <div >{row.getValue('className')}</div>,
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Created At' />,
    cell: ({ row }) => <div >{formatDateToLocal(row.getValue('createdAt'))}</div>,
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'lastSeenAt',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Last Seen At' />,
    cell: ({ row }) => <div>{formatDateToLocal(row.getValue('lastSeenAt'))}</div>,
    enableSorting: true,
    enableHiding: true,
  },
  {
    id: 'actions',
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
];




