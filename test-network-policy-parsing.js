// Test script for network policy parsing
const YAML = require('yaml');

// Test YAML with both ingress and egress (multi-document)
const testYaml = `---
apiVersion: "networking.k8s.io/v1"
kind: "NetworkPolicy"
metadata:
  labels:
    app.kubernetes.io/part-of: "kubepakt"
  name: "kubepakt-allow-ingress-to-app-worker"
  namespace: "test-local"
spec:
  ingress:
  - from:
    - ipBlock:
        cidr: "*************/32"
    ports:
    - port: 8091
      protocol: "TCP"
  - from:
    - ipBlock:
        cidr: "*************/32"
    ports:
    - port: 8091
      protocol: "TCP"
  podSelector:
    matchLabels:
      app: "worker"
  policyTypes:
  - "Ingress"
---
apiVersion: "networking.k8s.io/v1"
kind: "NetworkPolicy"
metadata:
  labels:
    app.kubernetes.io/part-of: "kubepakt"
  name: "kubepakt-allow-egress-from-app-worker"
  namespace: "test-local"
spec:
  egress:
  - ports:
    - port: 8080
      protocol: "TCP"
    to:
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: "test-mobilveportalyazilimlar"
      podSelector:
        matchLabels:
          app: "kepservicetestbtkgovtr"
  - ports:
    - port: 53
      protocol: "UDP"
    to:
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: "kube-system"
      podSelector:
        matchLabels:
          k8s-app: "kube-dns"
  podSelector:
    matchLabels:
      app: "worker"
  policyTypes:
  - "Egress"`;

// Simulate the parsing logic
function parseSingleNetworkPolicy(policy, ruleIdOffset = 0) {
  const rules = [];
  let ruleId = ruleIdOffset + 1;

  // Parse ingress rules
  if (policy.spec?.ingress) {
    policy.spec.ingress.forEach((ingressRule, index) => {
      const ports = ingressRule.ports || [];
      const portString = ports.length > 0
        ? ports.map(p => p.port || 'Any').join(', ')
        : 'Any';

      const protocol = ports.length > 0 && ports[0].protocol ? ports[0].protocol.toUpperCase() : 'Any';

      if (ingressRule.from && ingressRule.from.length > 0) {
        ingressRule.from.forEach((from) => {
          let source = 'Any';
          let sourceType = 'any';

          if (from.podSelector) {
            const labels = from.podSelector.matchLabels || {};
            source = Object.keys(labels).length > 0
              ? Object.entries(labels).map(([k, v]) => `${k}=${v}`).join(', ')
              : 'All Pods';
            sourceType = 'pod';
          } else if (from.namespaceSelector) {
            const labels = from.namespaceSelector.matchLabels || {};
            source = Object.keys(labels).length > 0
              ? `NS: ${Object.entries(labels).map(([k, v]) => `${k}=${v}`).join(', ')}`
              : 'All Namespaces';
            sourceType = 'namespace';
          } else if (from.ipBlock) {
            source = from.ipBlock.cidr || 'External';
            sourceType = 'external';
          }

          const targetLabels = policy.spec.podSelector?.matchLabels || {};
          const destination = Object.keys(targetLabels).length > 0
            ? Object.entries(targetLabels).map(([k, v]) => `${k}=${v}`).join(', ')
            : 'All Pods';

          rules.push({
            id: ruleId++,
            type: 'ingress',
            source,
            sourceType,
            destination,
            destinationType: 'pod',
            port: portString,
            protocol,
            description: `Ingress rule ${index + 1} - Allow traffic from ${sourceType}`
          });
        });
      }
    });
  }

  // Parse egress rules
  if (policy.spec?.egress) {
    policy.spec.egress.forEach((egressRule, index) => {
      const ports = egressRule.ports || [];
      const portString = ports.length > 0
        ? ports.map(p => p.port || 'Any').join(', ')
        : 'Any';

      const protocol = ports.length > 0 && ports[0].protocol ? ports[0].protocol.toUpperCase() : 'Any';

      const sourceLabels = policy.spec.podSelector?.matchLabels || {};
      const source = Object.keys(sourceLabels).length > 0
        ? Object.entries(sourceLabels).map(([k, v]) => `${k}=${v}`).join(', ')
        : 'All Pods';

      if (egressRule.to && egressRule.to.length > 0) {
        egressRule.to.forEach((to) => {
          let destination = 'Any';
          let destinationType = 'any';

          if (to.podSelector) {
            const labels = to.podSelector.matchLabels || {};
            destination = Object.keys(labels).length > 0
              ? Object.entries(labels).map(([k, v]) => `${k}=${v}`).join(', ')
              : 'All Pods';
            destinationType = 'pod';
          } else if (to.namespaceSelector) {
            const labels = to.namespaceSelector.matchLabels || {};
            destination = Object.keys(labels).length > 0
              ? `NS: ${Object.entries(labels).map(([k, v]) => `${k}=${v}`).join(', ')}`
              : 'All Namespaces';
            destinationType = 'namespace';
          } else if (to.ipBlock) {
            destination = to.ipBlock.cidr || 'External';
            destinationType = 'external';
          }

          rules.push({
            id: ruleId++,
            type: 'egress',
            source,
            sourceType: 'pod',
            destination,
            destinationType,
            port: portString,
            protocol,
            description: `Egress rule ${index + 1} - Allow traffic to ${destinationType}`
          });
        });
      }
    });
  }

  return {
    rules,
    nextRuleId: ruleId,
    policyInfo: {
      name: policy.metadata?.name || 'Unknown',
      namespace: policy.metadata?.namespace || 'default',
      podSelector: policy.spec?.podSelector || {},
      policyTypes: policy.spec?.policyTypes || []
    }
  };
}

function parseNetworkPolicy(yamlContent) {
  try {
    // Split by document separator to handle multiple policies
    const documents = yamlContent.split(/^---\s*$/m).filter(doc => doc.trim());
    
    if (documents.length === 0) {
      return null;
    }

    const allRules = [];
    let combinedPolicyTypes = [];
    let primaryPolicy = null;
    let ruleIdOffset = 0;

    console.log(`Found ${documents.length} documents`);

    // Parse each document
    for (const doc of documents) {
      const policy = YAML.parse(doc.trim());
      
      if (!policy || policy.kind !== 'NetworkPolicy') {
        continue;
      }

      console.log(`Processing policy: ${policy.metadata?.name}`);

      // Use the first policy as the primary one for metadata
      if (!primaryPolicy) {
        primaryPolicy = policy;
      }

      const { rules, nextRuleId, policyInfo } = parseSingleNetworkPolicy(policy, ruleIdOffset);
      allRules.push(...rules);
      ruleIdOffset = nextRuleId - 1;

      // Combine policy types
      if (policyInfo.policyTypes) {
        combinedPolicyTypes = [...new Set([...combinedPolicyTypes, ...policyInfo.policyTypes])];
      }
    }

    if (!primaryPolicy || allRules.length === 0) {
      return null;
    }

    return {
      name: primaryPolicy.metadata?.name || 'Unknown',
      namespace: primaryPolicy.metadata?.namespace || 'default',
      podSelector: primaryPolicy.spec?.podSelector || {},
      policyTypes: combinedPolicyTypes,
      rules: allRules
    };
  } catch (error) {
    console.error('Error parsing network policy:', error);
    return null;
  }
}

// Test the parsing
console.log('Testing network policy parsing...');
const result = parseNetworkPolicy(testYaml);

if (result) {
  console.log('✅ Parsing successful!');
  console.log(`Policy Name: ${result.name}`);
  console.log(`Namespace: ${result.namespace}`);
  console.log(`Policy Types: ${result.policyTypes.join(', ')}`);
  console.log(`Total Rules: ${result.rules.length}`);
  console.log('\nRules:');
  result.rules.forEach(rule => {
    console.log(`  ${rule.id}: ${rule.type.toUpperCase()} - ${rule.source} -> ${rule.destination} (${rule.port}/${rule.protocol})`);
  });
} else {
  console.log('❌ Parsing failed!');
}
