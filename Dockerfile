# 1. Base image
FROM node:20-alpine AS base

# pnpm yükle
RUN corepack enable && corepack prepare pnpm@latest --activate

WORKDIR /app

# 2. Dependencies
FROM base AS deps

COPY pnpm-lock.yaml package.json ./
RUN pnpm install --frozen-lockfile

# 3. Builder (tsc + vite build)
FROM base AS builder

COPY --from=deps /app/node_modules ./node_modules
COPY . .

# TypeScript build + Vite build (prod)
RUN pnpm run build

# 4. Runtime (Nginx)
FROM nginx:stable-alpine AS runner

# Nginx config kopyala
COPY nginx-prod.conf /etc/nginx/nginx.conf

# Build çıkışını Nginx'e kopyala
COPY --from=builder /app/dist /usr/share/nginx/html

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
