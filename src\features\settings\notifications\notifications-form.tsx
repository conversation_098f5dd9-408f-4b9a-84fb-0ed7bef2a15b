import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useEffect, useRef, useState } from 'react'
import { showSubmittedData } from '@/utils/show-submitted-data'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Switch } from '@/components/ui/switch'
import axios_api from '@/lib/axios_api'
import { useAuthStore } from '@/stores/authStore'
import { toast } from 'sonner'

// Define the user profile interface
interface UserProfile {
  id: number
  email: string | null
  username: string
  enabled: boolean
  clusternotification: boolean
  workloadnotification: boolean
  networknotification: boolean
  storagenotification: boolean
  confignotification: boolean
  securitynotification: boolean
  userRoles: Array<{
    id: number
    role: {
      id: number
      roleName: string
    }
  }>
  displayName: string | null
  inviteTokenExpiry: string | null
  inviteToken: string | null
}

const notificationsFormSchema = z.object({
  type: z.enum(['all', 'none'], {
    required_error: 'You need to select a notification type.',
  }),
  cluster_resource: z.boolean().default(false).optional(),
  workload_resource: z.boolean().default(false).optional(),
  network_resource: z.boolean().default(false).optional(),
  storage_resource: z.boolean().default(false).optional(),
  config_resource: z.boolean().default(false).optional(),
  security_resource: z.boolean().default(false).optional(),
})

type NotificationsFormValues = z.infer<typeof notificationsFormSchema>

// Default values before API data is loaded
const defaultValues: Partial<NotificationsFormValues> = {
  type: 'none',
  cluster_resource: false,
  workload_resource: false,
  network_resource: false,
  storage_resource: false,
  config_resource: false,
  security_resource: false,
}

export function NotificationsForm() {
  const [loading, setLoading] = useState(false)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const { user } = useAuthStore((state) => state.auth)
  
  const form = useForm<NotificationsFormValues>({
    resolver: zodResolver(notificationsFormSchema),
    defaultValues,
  })
  
  // Use a ref to prevent infinite loops
  const isUpdatingFromType = useRef(false)
  const isUpdatingFromSwitches = useRef(false)

  // Fetch user profile data when component mounts
  useEffect(() => {
    const fetchUserProfile = async () => {
       if (!user?.email) {
        toast.error("User email attribute is missing. Please ensure the user has a valid email.")
        return
      }

      setLoading(true)
      try {
        const response = await axios_api.post('/auth/profile', {
          username: user.accountNo,
        })
        
        if (response.data) {
          setUserProfile(response.data)
          
          // Update form values with the fetched notification settings
          const profile = response.data
          
          // Set individual notification values
          form.setValue('cluster_resource', profile.clusternotification || false)
          form.setValue('workload_resource', profile.workloadnotification || false)
          form.setValue('network_resource', profile.networknotification || false)
          form.setValue('storage_resource', profile.storagenotification || false)
          form.setValue('config_resource', profile.confignotification || false)
          form.setValue('security_resource', profile.securitynotification || false)
          
          // Determine the type based on notification settings
          const allEnabled = 
            profile.clusternotification && 
            profile.workloadnotification && 
            profile.networknotification && 
            profile.storagenotification && 
            profile.confignotification && 
            profile.securitynotification
            
          const noneEnabled = 
            !profile.clusternotification && 
            !profile.workloadnotification && 
            !profile.networknotification && 
            !profile.storagenotification && 
            !profile.confignotification && 
            !profile.securitynotification
            
          if (allEnabled) {
            form.setValue('type', 'all')
          } else if (noneEnabled) {
            form.setValue('type', 'none')
          }
        }
      } catch (err) {
      } finally {
        setLoading(false)
      }
    }

    fetchUserProfile()
  }, [user, form])

  // Watch the type field to update all switches when it changes
  const notificationType = form.watch('type')
  
  // Update all resource switches when notification type changes
  useEffect(() => {
    if (isUpdatingFromSwitches.current) {
      isUpdatingFromSwitches.current = false
      return
    }
    
    isUpdatingFromType.current = true
    
    if (notificationType === 'all') {
      form.setValue('cluster_resource', true, { shouldDirty: true })
      form.setValue('workload_resource', true, { shouldDirty: true })
      form.setValue('network_resource', true, { shouldDirty: true })
      form.setValue('storage_resource', true, { shouldDirty: true })
      form.setValue('config_resource', true, { shouldDirty: true })
      form.setValue('security_resource', true, { shouldDirty: true })
    } else if (notificationType === 'none') {
      form.setValue('cluster_resource', false, { shouldDirty: true })
      form.setValue('workload_resource', false, { shouldDirty: true })
      form.setValue('network_resource', false, { shouldDirty: true })
      form.setValue('storage_resource', false, { shouldDirty: true })
      form.setValue('config_resource', false, { shouldDirty: true })
      form.setValue('security_resource', false, { shouldDirty: true })
    }
    
    setTimeout(() => {
      isUpdatingFromType.current = false
    }, 0)
  }, [notificationType, form])

  // Watch all resource switches
  const cluster = form.watch('cluster_resource')
  const workload = form.watch('workload_resource')
  const network = form.watch('network_resource')
  const storage = form.watch('storage_resource')
  const config = form.watch('config_resource')
  const security = form.watch('security_resource')

  // Update notification type based on resource switches
  useEffect(() => {
    if (isUpdatingFromType.current) {
      return
    }
    
    isUpdatingFromSwitches.current = true
    
    const allSelected = 
      cluster === true && 
      workload === true && 
      network === true && 
      storage === true && 
      config === true && 
      security === true
      
    const noneSelected = 
      cluster === false && 
      workload === false && 
      network === false && 
      storage === false && 
      config === false && 
      security === false
    
    if (allSelected) {
      form.setValue('type', 'all', { shouldDirty: true })
    } else if (noneSelected) {
      form.setValue('type', 'none', { shouldDirty: true })
    }
    
    setTimeout(() => {
      isUpdatingFromSwitches.current = false
    }, 0)
  }, [cluster, workload, network, storage, config, security, form])

  async function onSubmit(data: NotificationsFormValues) {
    try {
      console.log("x"+userProfile?.username)
      if (userProfile) {
        await axios_api.post('/auth/profile/update', {
          username: userProfile.username,
          clusternotification: data.cluster_resource,
          workloadnotification: data.workload_resource,
          networknotification: data.network_resource,
          storagenotification: data.storage_resource,
          confignotification: data.config_resource,
          securitynotification: data.security_resource
        })
        
        toast.success('Notification settings updated successfully')
      }
    } catch (err) {
    
    }
    
    // For debugging
    showSubmittedData(data)
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className='space-y-8'
      >
        <FormField
          control={form.control}
          name='type'
          render={({ field }) => (
            <FormItem className='relative space-y-3'>
              <FormLabel>Notify me about...</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  value={field.value}
                  className='flex flex-col space-y-1'
                >
                  <FormItem className='flex items-center space-y-0 space-x-3'>
                    <FormControl>
                      <RadioGroupItem value='all' />
                    </FormControl>
                    <FormLabel className='font-normal'>
                      All notifications
                    </FormLabel>
                  </FormItem>
                  <FormItem className='flex items-center space-y-0 space-x-3'>
                    <FormControl>
                      <RadioGroupItem value='none' />
                    </FormControl>
                    <FormLabel className='font-normal'>Nothing</FormLabel>
                  </FormItem>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className='relative'>
          <h3 className='mb-4 text-lg font-medium'>Email Notifications</h3>
          <div className='space-y-4'>
            <FormField
              control={form.control}
              name='cluster_resource'
              render={({ field }) => (
                <FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
                  <div className='space-y-0.5'>
                    <FormLabel className='text-base'>
                      Cluster Resource
                    </FormLabel>
                    <FormDescription>
                      Receive notifications about nodes, namespaces, and other cluster-level resources.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name='workload_resource'
              render={({ field }) => (
                <FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
                  <div className='space-y-0.5'>
                    <FormLabel className='text-base'>
                      Workload Resource
                    </FormLabel>
                    <FormDescription>
                      Receive notifications about deployments, pods, cronjobs, and other workload resources.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name='network_resource'
              render={({ field }) => (
                <FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
                  <div className='space-y-0.5'>
                    <FormLabel className='text-base'>Network Resource</FormLabel>
                    <FormDescription>
                      Receive notifications about services, ingresses, and other network resources.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name='storage_resource'
              render={({ field }) => (
                <FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
                  <div className='space-y-0.5'>
                    <FormLabel className='text-base'>Storage Resource</FormLabel>
                    <FormDescription>
                      Receive notifications about persistent volumes, volume claims, and other storage resources.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name='config_resource'
              render={({ field }) => (
                <FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
                  <div className='space-y-0.5'>
                    <FormLabel className='text-base'>Config Resource</FormLabel>
                    <FormDescription>
                      Receive notifications about configmaps and other configuration resources.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name='security_resource'
              render={({ field }) => (
                <FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
                  <div className='space-y-0.5'>
                    <FormLabel className='text-base'>Security Resource</FormLabel>
                    <FormDescription>
                      Receive notifications about secrets, service accounts, and other security-related resources.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </div>
        <Button type='submit' disabled={loading}>
          {loading ? 'Loading...' : 'Update notifications'}
        </Button>
      </form>
    </Form>
  )
}
