import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import axios_api from '@/lib/axios_api'
import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

export function Dashboard_Index() {
  const [events, setEvents] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [searchTerm, setSearchTerm] = useState('')
  const eventsPerPage = 4

  useEffect(() => {
    async function fetchEvents() {
      try {
        setIsLoading(true)
        const response = await axios_api.get("/k8s/events")
        const data = response.data
        // Sort events by lastTimestamp in descending order (newest first)
        const sortedEvents = [...data].sort((a, b) => 
          new Date((b as any).lastTimestamp).getTime() - new Date((a as any).lastTimestamp).getTime()
        )
        setEvents(sortedEvents as any)
      } catch (err) {
      } finally {
        setIsLoading(false)
      }
    }

    fetchEvents()
  }, [])

  // Filter events based on search term
  const filteredEvents = events.filter(event => 
    (event.message || '').toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Get current events for pagination
  const indexOfLastEvent = currentPage * eventsPerPage
  const indexOfFirstEvent = indexOfLastEvent - eventsPerPage
  const currentEvents = filteredEvents.slice(indexOfFirstEvent, indexOfLastEvent)
  const totalPages = Math.ceil(filteredEvents.length / eventsPerPage)

  const handleNextPage = () => {
    if (currentPage < totalPages) setCurrentPage(currentPage + 1)
  }

  const handlePrevPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1)
  }

  if (isLoading) return <div>Loading events...</div>

  return (
    <div className='space-y-4'>
      <Input
        placeholder="Search by message..."
        value={searchTerm}
        onChange={(e) => {
          setSearchTerm(e.target.value)
          setCurrentPage(1) // Reset to first page when searching
        }}
        className="mb-4"
      />
      
      <div className='space-y-8'>
        {currentEvents.length > 0 ? (
          <>
            {currentEvents.map((event, index) => (
              <div key={index} className='flex items-center gap-4'>
                <Avatar className='h-16 w-16'>
                  <AvatarImage src={`/avatars/0${(index % 5) + 1}.png`} alt='Event' />
                  <AvatarFallback className="font-medium text-[9px]">
                    {event.reason?.substring(0, 10) || 'EVENT'}
                  </AvatarFallback>
                </Avatar>
                <div className='flex flex-1 flex-wrap items-center justify-between'>
                  <div className='space-y-1'>
                  <div className='font-medium'>
                    <p className='text-xs'>{new Date(event.firstTimestamp).toLocaleString()} - {new Date(event.lastTimestamp).toLocaleString()  }</p>
                    <p className='text-xs text-muted-foreground'></p>
                  </div>
                    <p className='text-sm leading-none font-medium'>{event.name}</p>
                    <p className='text-muted-foreground text-sm'>
                      {event.namespace} - {event.type}
                    </p>
                    <p className='text-muted-foreground text-xs'>
                      {event.message}
                    </p>
                  </div>   
                </div>
              </div>
            ))}
            
            {/* Pagination controls */}
            <div className="flex justify-between items-center mt-4">
              <Button 
                variant="outline" 
                onClick={handlePrevPage} 
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <span className="text-sm">
                Page {currentPage} / {totalPages}
              </span>
              <Button 
                variant="outline" 
                onClick={handleNextPage} 
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          </>
        ) : (
          <div className='text-center py-4'>No events found</div>
        )}
      </div>
    </div>
  )
}
