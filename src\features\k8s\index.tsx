import { useState, useEffect } from 'react'
import {
  IconAdjustmentsHorizontal,
  IconSortAscendingLetters,
  IconSortDescendingLetters,
} from '@tabler/icons-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Loader2 } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { useNavigate } from '@tanstack/react-router'


// Import icons
import nsLogo from '@/assets/namespace.svg'
import appLogo from '@/assets/application.svg'
import serviceLogo from '@/assets/service.svg'
import cmLogo from '@/assets/configmap.svg'
import secretLogo from '@/assets/secret.svg'
import ingressLogo from '@/assets/ingress.svg'
import volumeLogo from '@/assets/volume.svg'
import axios_api from '@/lib/axios_api'

interface DashboardData {
  counts: {
    configmaps: number
    cronjobs: number
    daemonsets: number
    deployments: number
    ingresses: number
    namespaces: number
    pods: number
    secrets: number
    services: number
    volumes: number
    volumesclaim: number
    nodes: number
  }
}

interface ResourceCard {
  title: string
  path: string
  logo: string
  count: number
  description: string
  type: string
}

const resourceTypeText = new Map<string, string>([
  ['all', 'All Resources'],
  ['cluster', 'Cluster Resources'],
  ['workload', 'Workload Resources'],
  ['network', 'Network Resources'],
  ['storage', 'Storage Resources'],
  ['config', 'Configuration'],
]);

export default function K8sOverview() {
  const navigate = useNavigate()
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error] = useState<string | null>(null)
  const [sort, setSort] = useState('ascending')
  const [resourceType, setResourceType] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true)
      try {
        const response = await axios_api.get('/k8s/counts')
        const transformedData: DashboardData = {
          counts: {
            configmaps: response.data.configmaps,
            cronjobs: response.data.cronjobs,
            daemonsets: response.data.daemonsets,
            deployments: response.data.deployments,
            ingresses: response.data.ingresses,
            namespaces: response.data.namespaces,
            pods: response.data.pods,
            secrets: response.data.secrets,
            services: response.data.services,
            volumes: response.data.persistentvolumes,
            volumesclaim: response.data.persistentvolumeclaims,
            nodes: response.data.nodes
          }
        }
        setDashboardData(transformedData)
      } catch (err) {
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  if (loading) {
    return (
      <>
        <Header fixed>
          <Search />
          <div className='ml-auto flex items-center space-x-4'>
            <ThemeSwitch />
            <ProfileDropdown />
          </div>
        </Header>
        <Main fixed>
          <div className="flex items-center justify-center h-full">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Loading Kubernetes data...</span>
          </div>
        </Main>
      </>
    )
  }

  if (error) {
    return (
      <>
        <Header fixed>
          <Search />
          <div className='ml-auto flex items-center space-x-4'>
            <ThemeSwitch />
            <ProfileDropdown />
          </div>
        </Header>
        <Main fixed>
          <div className="flex flex-col items-center justify-center h-full">
            <div className="text-destructive text-xl mb-2">Error</div>
            <div>{error}</div>
            <Button 
              variant="outline" 
              className="mt-4"
              onClick={() => window.location.reload()}
            >
              Retry
            </Button>
          </div>
        </Main>
      </>
    )
  }

  // Define resource cards data
  const resourceCards: ResourceCard[] = [
    { 
      title: 'Nodes', 
      path: '/k8s/nodes', 
      logo: volumeLogo, 
      count: dashboardData?.counts.nodes || 0,
      description: 'Manage Kubernetes nodes in your cluster',
      type: 'cluster'
    },
    { 
      title: 'Namespaces', 
      path: '/k8s/namespaces', 
      logo: nsLogo, 
      count: dashboardData?.counts.namespaces || 0,
      description: 'Manage Kubernetes namespaces',
      type: 'cluster'
    },
    { 
      title: 'Applications', 
      path: '/k8s/deployments', 
      logo: appLogo, 
      count: dashboardData?.counts.deployments || 0,
      description: 'Manage Kubernetes deployments',
      type: 'workload'
    },
    { 
      title: 'Pods', 
      path: '/k8s/pods', 
      logo: volumeLogo, 
      count: dashboardData?.counts.pods || 0,
      description: 'Manage Kubernetes pods',
      type: 'workload'
    },
    { 
      title: 'CronJobs', 
      path: '/k8s/cronjobs', 
      logo: volumeLogo, 
      count: dashboardData?.counts.cronjobs || 0,
      description: 'Manage Kubernetes cronjobs',
      type: 'workload'
    },
    { 
      title: 'Services', 
      path: '/k8s/services', 
      logo: serviceLogo, 
      count: dashboardData?.counts.services || 0,
      description: 'Manage Kubernetes services',
      type: 'network'
    },
    { 
      title: 'Ingresses', 
      path: '/k8s/ingresses', 
      logo: ingressLogo, 
      count: dashboardData?.counts.ingresses || 0,
      description: 'Manage Kubernetes ingresses',
      type: 'network'
    },
    { 
      title: 'ConfigMaps', 
      path: '/k8s/configmaps', 
      logo: cmLogo, 
      count: dashboardData?.counts.configmaps || 0,
      description: 'Manage Kubernetes configmaps',
      type: 'config'
    },
    { 
      title: 'Secrets', 
      path: '/k8s/secrets', 
      logo: secretLogo, 
      count: dashboardData?.counts.secrets || 0,
      description: 'Manage Kubernetes secrets',
      type: 'config'
    },
    { 
      title: 'Volumes', 
      path: '/k8s/volumes', 
      logo: volumeLogo, 
      count: dashboardData?.counts.volumes || 0,
      description: 'Manage Kubernetes persistent volumes',
      type: 'storage'
    },
    { 
      title: 'Volume Claims', 
      path: '/k8s/volumes_claims', 
      logo: volumeLogo, 
      count: dashboardData?.counts.volumesclaim || 0,
      description: 'Manage Kubernetes persistent volume claims',
      type: 'storage'
    }
  ]

  // Filter and sort resources
  const filteredResources = resourceCards
    .sort((a, b) =>
      sort === 'ascending'
        ? a.title.localeCompare(b.title)
        : b.title.localeCompare(a.title)
    )
    .filter((resource) =>
      resourceType === 'all'
        ? true
        : resource.type === resourceType
    )
    .filter((resource) => 
      resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.description.toLowerCase().includes(searchTerm.toLowerCase())
    )

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Content ===== */}
      <Main fixed>
        <div>
          <h1 className='text-2xl font-bold tracking-tight'>
            Kubernetes Resources
          </h1>
          <p className='text-muted-foreground'>
            Here&apos;s a list of your Kubernetes resources for management!
          </p>
        </div>
        <div className='my-4 flex items-end justify-between sm:my-0 sm:items-center'>
          <div className='flex flex-col gap-4 sm:my-4 sm:flex-row'>
            <Input
              placeholder='Filter resources...'
              className='h-9 w-40 lg:w-[250px]'
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Select value={resourceType} onValueChange={setResourceType}>
              <SelectTrigger className='w-36'>
                <SelectValue>{resourceTypeText.get(resourceType)}</SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Resources</SelectItem>
                <SelectItem value='cluster'>Cluster Resources</SelectItem>
                <SelectItem value='workload'>Workload Resources</SelectItem>
                <SelectItem value='network'>Network Resources</SelectItem>
                <SelectItem value='storage'>Storage Resources</SelectItem>
                <SelectItem value='config'>Configuration</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Select value={sort} onValueChange={setSort}>
            <SelectTrigger className='w-16'>
              <SelectValue>
                <IconAdjustmentsHorizontal size={18} />
              </SelectValue>
            </SelectTrigger>
            <SelectContent align='end'>
              <SelectItem value='ascending'>
                <div className='flex items-center gap-4'>
                  <IconSortAscendingLetters size={16} />
                  <span>Ascending</span>
                </div>
              </SelectItem>
              <SelectItem value='descending'>
                <div className='flex items-center gap-4'>
                  <IconSortDescendingLetters size={16} />
                  <span>Descending</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Separator className='shadow-sm' />
        {filteredResources.length > 0 ? (
          <ul className='faded-bottom no-scrollbar grid gap-4 overflow-auto pt-4 pb-16 md:grid-cols-2 lg:grid-cols-3'>
            {filteredResources.map((resource) => (
              <li
                key={resource.title}
                className='rounded-lg border p-4 hover:shadow-md cursor-pointer'
                onClick={() => navigate({ to: resource.path })}
              >
                <div className='mb-8 flex items-center justify-between'>
                  <div className='bg-muted flex size-10 items-center justify-center rounded-lg p-2'>
                    <img src={resource.logo} alt={`${resource.title} logo`} width='20' height='20' />
                  </div>
                  <Badge variant='outline' className='text-lg font-semibold'>
                    {resource.count}
                  </Badge>
                </div>
                <div>
                  <h2 className='mb-1 font-semibold'>{resource.title}</h2>
                  <p className='line-clamp-2 text-muted-foreground'>{resource.description}</p>
                </div>
              </li>
            ))}
          </ul>
        ) : (
          <div className='flex flex-col items-center justify-center py-16 text-center'>
            <div className='text-muted-foreground mb-2'>No resources found matching "{searchTerm}"</div>
            <Button 
              variant='outline' 
              onClick={() => {
                setSearchTerm('')
                setResourceType('all')
              }}
              className='mt-2'
            >
              Clear filters
            </Button>
          </div>
        )}
      </Main>
    </>
  )
}



