import { useState, useEffect, use<PERSON><PERSON>back } from "react";
import {
    <PERSON>,
    <PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

import { columns } from "./component/columns";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import axios_api from "@/lib/axios_api";
import { DataTable } from "../component/data-table";

// Define the types
interface LabelData {
    key: string;
    value: string;
    hasNetworkPolicy: boolean;
}

interface PodLabelResponse {
    pods: {
        name: string;
        labels: { [key: string]: string };
        hasNetworkPolicy: boolean;
    }[];
}

interface NamespaceData {
    id: string;
    name: string;
    status: string;
    quota: string;
    createdAt: string;
    labels: string;
    annotation: string;
    lastSeenAt: string;
}

interface SourceSelection {
    namespace: string;
    appLabel: string;
}

interface DestinationSelection {
    ingressEgress: string;
    portType: string;
    portNumber: string;
    destinationType: string;
    destinationNamespace: string;
    destinationNamespaceLabel: string;
    destinationPodLabel: string;
    destinationCustomIP: string;
}

interface NetworkPolicy {
    name: string;
    namespace: string;
    creationTimestamp: string;
    podSelector: any;
    policyTypes: string[];
}

const SecurityPolicyRule = () => {
    const [activeTab, setActiveTab] = useState("policies");
    const [sourceDataAdded, setSourceDataAdded] = useState(false);
    const [networkPolicies, setNetworkPolicies] = useState<NetworkPolicy[]>([]);
    const [loading, setLoading] = useState(false);

    // Source Tab State
    const [sourceNamespace, setSourceNamespace] = useState("");
    const [labels, setLabels] = useState<LabelData[]>([]);
    const [selectedLabel, setSelectedLabel] = useState<string | undefined>(undefined);
    const [namespaces, setNamespaces] = useState<NamespaceData[]>([]);
    const [sourceSelection, setSourceSelection] = useState<SourceSelection | null>(null);
    const [sourceError, setSourceError] = useState<string | null>(null);

    // Destination Tab State
    const [destinationIngressEgress, setDestinationIngressEgress] = useState("");
    const [destinationPortType, setDestinationPortType] = useState("TCP");
    const [destinationPortNumber, setDestinationPortNumber] = useState("");
    const [destinationType, setDestinationType] = useState("");
    const [destinationNamespace, setDestinationNamespace] = useState("");
    const [destinationNamespaceLabel, setDestinationNamespaceLabel] = useState("");
    const [destinationPodLabel, setDestinationPodLabel] = useState("");
    const [destinationCustomIP, setDestinationCustomIP] = useState("");
    const [destinationLabels, setDestinationLabels] = useState<LabelData[]>([]);
    const [destinationSelections, setDestinationSelections] = useState<DestinationSelection[]>([]);

    const fetchNetworkPolicies = async () => {
        try {
            setLoading(true);
            const policiesResponse = await axios_api.get("/k8s/networkpolicies");
            const policies = policiesResponse.data;
            setNetworkPolicies(policies);
            return policies;
        } catch (error) {
            return [];
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchNetworkPolicies();
    }, []);

    useEffect(() => {
        const fetchNamespaces = async () => {
            try {
                const response = await axios_api.get<NamespaceData[]>("/k8s/namespaces");
                setNamespaces(response.data);
            } catch (error) {
            }
        };

        fetchNamespaces();
    }, []);

    const fetchLabels = useCallback(async (namespace: string) => {
        try {
            const response = await axios_api.get<PodLabelResponse>(`/k8s/pod-labels-with-networkpolicy`, {
                params: {
                    namespace: namespace,
                },
            });

            const allLabels: { key: string; value: string; hasNetworkPolicy: boolean }[] = [];

            response.data.pods.forEach((pod) => {
                if (pod.labels && pod.labels.app) {
                    allLabels.push({
                        key: pod.labels.app,
                        value: pod.labels.app,
                        hasNetworkPolicy: pod.hasNetworkPolicy,
                    });
                }
            });

            const uniqueAppsMap: Map<string, { key: string; value: string; hasNetworkPolicy: boolean }> = new Map();
            allLabels.forEach((label) => {
                if (!uniqueAppsMap.has(label.value)) {
                    uniqueAppsMap.set(label.value, label);
                }
            });

            const uniqueApps: { key: string; value: string; hasNetworkPolicy: boolean }[] = Array.from(uniqueAppsMap.values());

            setLabels(uniqueApps);
        } catch (error) {
            console.error("Error fetching labels:", error);
            toast.error("Failed to fetch labels.");
            setLabels([]);
        }
    }, [axios_api]);

    useEffect(() => {
        const fetchDestinationPodLabels = async () => {
            if (destinationType === "pod" && destinationNamespace) {
                try {
                    const response = await axios_api.get<PodLabelResponse>(`/k8s/pod-labels`, {
                        params: {
                            namespace: destinationNamespace,
                        },
                    });

                    const allLabels: { key: string; value: string }[] = [];

                    response.data.pods.forEach((pod) => {
                        for (const key in pod.labels) {
                            if (pod.labels.hasOwnProperty(key)) {
                                allLabels.push({ key: key, value: pod.labels[key] });
                            }
                        }
                    });

                    const labelData: LabelData[] = allLabels.map((label) => ({
                        key: label.key,
                        value: label.value,
                        hasNetworkPolicy: false,
                    }));

                    setDestinationLabels(labelData);
                } catch (error) {
                    console.error("Error fetching destination pod labels:", error);
                    toast.error("Failed to fetch destination pod labels.");
                    setDestinationLabels([]);
                }
            } else {
                setDestinationLabels([]);
            }
        };

        fetchDestinationPodLabels();
    }, [destinationType, destinationNamespace, axios_api]);

    useEffect(() => {
        const fetchNamespaceLabels = async () => {
            if (destinationType === "namespace" && destinationNamespace) {
                try {
                    const selectedNamespaceData = namespaces.find((ns) => ns.name === destinationNamespace);

                    if (selectedNamespaceData && selectedNamespaceData.labels) {
                        try {
                            const labelsObject = JSON.parse(selectedNamespaceData.labels);
                            const labelArray: LabelData[] = Object.entries(labelsObject).map(([key, value]) => ({ key, value: String(value), hasNetworkPolicy: false }));
                            setDestinationLabels(labelArray);
                        } catch (error) {
                            console.error("Error parsing namespace labels:", error);
                            toast.error("Error parsing namespace labels.");
                            setDestinationLabels([]);
                        }
                    } else {
                        setDestinationLabels([]);
                    }
                } catch (error) {
                    console.error("Error fetching namespace labels:", error);
                    toast.error("Error fetching namespace labels.");
                    setDestinationLabels([]);
                }
            } else {
                setDestinationLabels([]);
            }
        };

        fetchNamespaceLabels();
    }, [destinationType, destinationNamespace, namespaces]);

    useEffect(() => {
        if (sourceNamespace) {
            fetchLabels(sourceNamespace);
        } else {
            setLabels([]);
            setSelectedLabel(undefined);
        }
    }, [sourceNamespace, fetchLabels]);

    const isValidIPorCIDR = (ip: string) => {
        const ipv4CidrRegex =
            /^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\/([0-9]|[1-2][0-9]|3[0-2]))?$/;
        return ipv4CidrRegex.test(ip);
    };

    const handleAddSource = () => {
        if (!sourceNamespace || !selectedLabel) {
            setSourceError("Please select both a namespace and an app label.");
            toast.error("Please select both a namespace and an app label.");
            return;
        }

        setSourceError(null);
        const newSourceSelection: SourceSelection = {
            namespace: sourceNamespace,
            appLabel: selectedLabel,
        };
        setSourceSelection(newSourceSelection);
        setSourceDataAdded(true);
        toast.success("Source data added successfully!");
    };

    const handleAddDestination = () => {
        if (
            !destinationIngressEgress ||
            !destinationPortType ||
            !destinationPortNumber ||
            (!destinationCustomIP &&
                ((destinationType === "namespace" && (!destinationNamespace || !destinationNamespaceLabel)) ||
                    (destinationType === "pod" && (!destinationNamespace || !destinationPodLabel)) ||
                    destinationType === undefined))
        ) {
            toast.error("Please fill in all required fields for the destination.");
            return;
        }

        const newDestinationSelection: DestinationSelection = {
            ingressEgress: destinationIngressEgress,
            portType: destinationPortType,
            portNumber: destinationPortNumber,
            destinationType: destinationType,
            destinationNamespace: destinationNamespace,
            destinationNamespaceLabel: destinationNamespaceLabel,
            destinationPodLabel: destinationPodLabel,
            destinationCustomIP: destinationCustomIP,
        };

        setDestinationSelections((prevSelections) => [...prevSelections, newDestinationSelection]);
        setDestinationIngressEgress("");
        setDestinationPortType("TCP");
        setDestinationPortNumber("");
        setDestinationType("");
        setDestinationNamespace("");
        setDestinationNamespaceLabel("");
        setDestinationPodLabel("");
        setDestinationCustomIP("");
        setDestinationLabels([]);
        toast.success("Destination added successfully!");
    };


    const handleSubmit = async () => {
        try {
            const sourceDataForBackend = sourceSelection
                ? {
                    namespace: sourceSelection.namespace,
                    appLabel: sourceSelection.appLabel,
                }
                : null;

            const data = {
                sourceSelection: sourceDataForBackend,
                destinationSelections: destinationSelections,
            };

            await axios_api.post("/k8s/networkpolicypatch", data);
            toast.success("Data submitted successfully!");
        } catch (error) {
            console.error("Error submitting data:", error);
            toast.error("Error submitting data.");
        }
    };

    return (
        <div className="w-full mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
                {/* Left side - Form */}
                <div className="lg:col-span-3">
                    <Card className="h-full">
                        <CardHeader>
                            <CardTitle>Network Policies</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <Tabs value={activeTab} onValueChange={setActiveTab}>
                                <TabsList className="mb-4">
                                    <TabsTrigger value="source">Select Source</TabsTrigger>
                                    <TabsTrigger value="destination" disabled={!sourceDataAdded}>
                                        Define Rules
                                    </TabsTrigger>
                                </TabsList>

                                <TabsContent value="source">
                                    <div className="grid gap-4">
                                        <div>
                                            <Label htmlFor="sourceNamespace" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed">
                                                Namespace
                                            </Label>
                                            <Select value={sourceNamespace} onValueChange={setSourceNamespace}>
                                                <SelectTrigger className="w-full">
                                                    <SelectValue placeholder="Select a namespace" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {namespaces.map((ns) => (
                                                        <SelectItem key={ns.id} value={ns.name}>
                                                            {ns.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </div>
                                        <div>
                                            <Label htmlFor="selectedLabel" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed">
                                                Label
                                            </Label>
                                            <Select value={selectedLabel} onValueChange={setSelectedLabel}>
                                                <SelectTrigger className="w-full">
                                                    <SelectValue placeholder="Select an app" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {labels.map((label) => (
                                                        <SelectItem
                                                            key={label.key}
                                                            value={label.value}
                                                            className={cn(label.hasNetworkPolicy ? "text-green-500" : "text-red-500")}
                                                        >
                                                            {label.value}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </div>
                                        {sourceError && <p className="text-red-500">{sourceError}</p>}
                                        <Button onClick={handleAddSource} className="w-full">
                                            Add Source
                                        </Button>
                                    </div>

                                    {sourceSelection && (
                                        <div className="mt-4">
                                            <Label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed">Source Selection:</Label>
                                            <Textarea
                                                readOnly
                                                value={JSON.stringify(sourceSelection, null, 2)}
                                                className="mt-2 resize-none"
                                            />
                                        </div>
                                    )}
                                </TabsContent>

                                <TabsContent value="destination">
                                    <div className="grid gap-4">
                                        <div>
                                            <Label htmlFor="destinationIngressEgress" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed">
                                                Ingress / Egress
                                            </Label>
                                            <Select value={destinationIngressEgress} onValueChange={setDestinationIngressEgress}>
                                                <SelectTrigger className="w-full">
                                                    <SelectValue placeholder="Select Type" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="ingress">Ingress</SelectItem>
                                                    <SelectItem value="egress">Egress</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div>
                                            <Label htmlFor="destinationPortType" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed">
                                                Destination Port Type
                                            </Label>
                                            <Select value={destinationPortType} onValueChange={setDestinationPortType}>
                                                <SelectTrigger className="w-full">
                                                    <SelectValue placeholder="Select Port Type" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="TCP">TCP</SelectItem>
                                                    <SelectItem value="UDP">UDP</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div>
                                            <Label htmlFor="destinationPortNumber" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed">
                                                Destination Port Number
                                            </Label>
                                            <Input
                                                type="number"
                                                id="destinationPortNumber"
                                                placeholder="Enter Port Number"
                                                value={destinationPortNumber}
                                                onChange={(e) => setDestinationPortNumber(e.target.value)}
                                                className="w-full"
                                            />
                                        </div>

                                        <div>
                                            <Label htmlFor="destinationType" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed">
                                                Destination Type
                                            </Label>
                                            <Select
                                                value={destinationType}
                                                onValueChange={(value) => {
                                                    setDestinationType(value);
                                                    setDestinationNamespace("");
                                                    setDestinationNamespaceLabel("");
                                                    setDestinationPodLabel("");
                                                    setDestinationCustomIP("");
                                                    setDestinationLabels([]);
                                                }}
                                            >
                                                <SelectTrigger className="w-full">
                                                    <SelectValue placeholder="Select Destination Type" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="namespace">Namespace</SelectItem>
                                                    <SelectItem value="pod">Pod</SelectItem>
                                                    <SelectItem value="custom">Custom IP</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        {destinationType === "namespace" && (
                                            <>
                                                <div>
                                                    <Label htmlFor="destinationNamespace" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed">
                                                        Namespace
                                                    </Label>
                                                    <Select value={destinationNamespace} onValueChange={setDestinationNamespace}>
                                                        <SelectTrigger className="w-full">
                                                            <SelectValue placeholder="Select Namespace" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {namespaces.map((ns) => (
                                                                <SelectItem key={ns.id} value={ns.name}>
                                                                    {ns.name}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                </div>
                                                <div>
                                                    <Label htmlFor="destinationNamespaceLabel" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed">
                                                        Namespace Label
                                                    </Label>
                                                    <Select value={destinationNamespaceLabel} onValueChange={setDestinationNamespaceLabel}>
                                                        <SelectTrigger className="w-full">
                                                            <SelectValue placeholder="Select Label" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {destinationLabels && destinationLabels.length > 0 ? (
                                                                destinationLabels.map((label) => (
                                                                    <SelectItem key={label.key} value={label.value}>
                                                                        {label.key}: {label.value}
                                                                    </SelectItem>
                                                                ))
                                                            ) : (
                                                                <SelectItem disabled value="no-labels">
                                                                    No labels found
                                                                </SelectItem>
                                                            )}
                                                        </SelectContent>
                                                    </Select>
                                                </div>
                                            </>
                                        )}

                                        {destinationType === "pod" && (
                                            <>
                                                <div>
                                                    <Label htmlFor="destinationNamespace" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed">
                                                        Namespace
                                                    </Label>
                                                    <Select value={destinationNamespace} onValueChange={setDestinationNamespace}>
                                                        <SelectTrigger className="w-full">
                                                            <SelectValue placeholder="Select Namespace" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {namespaces.map((ns) => (
                                                                <SelectItem key={ns.id} value={ns.name}>
                                                                    {ns.name}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                </div>
                                                <div>
                                                    <Label htmlFor="destinationPodLabel" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed">
                                                        Pod Label
                                                    </Label>
                                                    <Select value={destinationPodLabel} onValueChange={setDestinationPodLabel}>
                                                        <SelectTrigger className="w-full">
                                                            <SelectValue placeholder="Select Pod Label" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {destinationLabels && destinationLabels.length > 0 ? (
                                                                destinationLabels.map((label) => (
                                                                    <SelectItem key={label.key} value={label.value}>
                                                                        {label.key}: {label.value}
                                                                    </SelectItem>
                                                                ))
                                                            ) : (
                                                                <SelectItem disabled value="no-labels">
                                                                    No labels found
                                                                </SelectItem>
                                                            )}
                                                        </SelectContent>
                                                    </Select>
                                                </div>
                                            </>
                                        )}

                                        {destinationType === "custom" && (
                                            <div>
                                                <Label htmlFor="destinationCustomIP" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed">
                                                    Custom IP Address
                                                </Label>
                                                <Input
                                                    type="text"
                                                    id="destinationCustomIP"
                                                    placeholder="Enter IP Address or CIDR"
                                                    value={destinationCustomIP}
                                                    onChange={(e) => setDestinationCustomIP(e.target.value)}
                                                    className="w-full"
                                                />
                                                {!isValidIPorCIDR(destinationCustomIP) && destinationCustomIP && (
                                                    <p className="text-red-500 text-sm">Please enter a valid IP Address or CIDR Block.</p>
                                                )}
                                            </div>
                                        )}

                                        <Button onClick={handleAddDestination} className="w-full">
                                            Add Destination
                                        </Button>

                                        {destinationSelections.length > 0 && (
                                            <div className="mt-4">
                                                <Label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed">Destination Selections:</Label>
                                                <Textarea
                                                    readOnly
                                                    value={JSON.stringify(destinationSelections, null, 2)}
                                                    className="mt-2 resize-none"
                                                />
                                            </div>
                                        )}
                                        <Button onClick={handleSubmit} className="w-full">
                                            Submit
                                        </Button>
                                    </div>
                                </TabsContent>
                            </Tabs>
                        </CardContent>
                    </Card>
                </div>

                {/* Right side - Table */}
                <div className="lg:col-span-9">
                    <Card className="h-full">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle>Existing Policies</CardTitle>
                            <Button 
                                variant="outline" 
                                onClick={fetchNetworkPolicies}
                                disabled={loading}
                                size="sm"
                            >
                                Refresh
                            </Button>
                        </CardHeader>
                        <CardContent>
                            <DataTable 
                                columns={columns(fetchNetworkPolicies)} 
                                data={networkPolicies} 
                                loading={loading}
                            />
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    );
};

export default SecurityPolicyRule;
