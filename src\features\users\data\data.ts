import {
  IconShield,
  IconUsersGroup,
  IconUserShield,
} from '@tabler/icons-react'

// Update user types to match your API roles
export const userTypes = [
  {
    value: 'ROLE_ADMIN',
    label: 'ROLE_ADMIN',
    icon: IconUserShield,
  },
  {
    value: 'ROLE_SECURITY',
    label: 'ROLE_SECURITY',
    icon: IconShield,
  },
  {
    value: 'ROLE_OPERATOR',
    label: 'ROLE_OPERATOR',
    icon: IconUsersGroup,
  },
]

// Status colors
export const statusColors = {
  true: 'bg-teal-100/30 text-teal-900 dark:text-teal-200 border-teal-200',
  false: 'bg-destructive/10 dark:bg-destructive/50 text-destructive dark:text-primary border-destructive/10',
}
