import { Checkbox } from '@/components/ui/checkbox'
import { ColumnDef } from '@tanstack/react-table';
import { DataTableRowActions } from './data-table-row-actions';
import { DataTableColumnHeader } from '../../components/data-table-column-header';
import { formatDateToLocal } from '@/lib/dateformat';



interface DataEntity {
  id: string;
  name: string;
  role: string;
  status: string;
  cpu: string;
  memory: string;
  ipAddress: string;
  labels: string | Record<string, string>;
  annotation: string | Record<string, string>;
  lastSeenAt: string;
}

 

export const columns: ColumnDef<DataEntity>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
        className='translate-y-[2px]'
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label='Select row'
        className='translate-y-[2px]'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Name' />
    ),
    cell: ({ row }) => <div >{row.getValue('name')}</div>,
    enableSorting: true,
    enableHiding: false,
  },{
    accessorKey: 'role',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Role' />
    ),
    cell: ({ row }) => <div >{row.getValue('role')}</div>,
    enableSorting: true,
    enableHiding: false,
  },{
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Status' />
    ),
    cell: ({ row }) => <div >{row.getValue('status')}</div>,
    enableSorting: true,
    enableHiding: false,
  },{
    accessorKey: 'cpu',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Cpu' />
    ),
    cell: ({ row }) => <div >{row.getValue('cpu')}</div>,
    enableSorting: true,
    enableHiding: false,
  },{
    accessorKey: 'memory',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Memory' />
    ),
    cell: ({ row }) => <div >{row.getValue('memory')}</div>,
    enableSorting: true,
    enableHiding: false,
  },{
    accessorKey: 'ipAddress',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='IpAdress' />
    ),
    cell: ({ row }) => <div >{row.getValue('ipAddress')}</div>,
    enableSorting: true,
    enableHiding: false,
  },{
    accessorKey: 'nodeCidr',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Node Cıdr' />
    ),
    cell: ({ row }) => <div >{row.getValue('nodeCidr')}</div>,
    enableSorting: true,
    enableHiding: false,
  },{
    accessorKey: 'version',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Version' />
    ),
    cell: ({ row }) => <div >{row.getValue('version')}</div>,
    enableSorting: true,
    enableHiding: false,
  },{
    accessorKey: 'lastSeenAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='LastSeenAt' />
    ),
    cell: ({ row }) => <div>{formatDateToLocal(row.getValue('lastSeenAt'))}</div>,
    enableSorting: true,
    enableHiding: false,
  },
  {
    id: 'actions',
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
]


