import { z } from 'zod'

// Define the authority schema
const authoritySchema = z.object({
  authority: z.string()
})

// Updated user schema to match the API response
const userSchema = z.object({
  id: z.number(),
  username: z.string(),
  displayName: z.string().nullable(),
  roles: z.array(z.string()),
  enabled: z.boolean(),
  password: z.string().nullable(),
  authorities: z.array(authoritySchema),
  accountNonExpired: z.boolean(),
  credentialsNonExpired: z.boolean(),
  accountNonLocked: z.boolean()
})

export type User = z.infer<typeof userSchema>
export const userListSchema = z.array(userSchema)
