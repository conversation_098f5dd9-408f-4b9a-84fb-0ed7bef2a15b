import { useState } from 'react'
import { DotsHorizontalIcon } from '@radix-ui/react-icons'
import {  IconC<PERSON>, IconLabel, IconTagFilled, IconSettings, IconInfoCircle } from '@tabler/icons-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import axios_api from '@/lib/axios_api'

// NodeDetail interfaces based on the Java entity
interface NodeCondition {
  type: string;
  status: string;
  lastHeartbeatTime?: string;
  lastTransitionTime?: string;
  reason?: string;
  message?: string;
}

interface NodeAddress {
  type: string;
  address: string;
}

interface NodeCapacity {
  cpu?: string;
  memory?: string;
  pods?: string;
  ephemeralStorage?: string;
}

interface NodeAllocatable {
  cpu?: string;
  memory?: string;
  pods?: string;
  ephemeralStorage?: string;
}

interface SystemInfo {
  machineID?: string;
  systemUUID?: string;
  bootID?: string;
  kernelVersion?: string;
  osImage?: string;
  containerRuntimeVersion?: string;
  kubeletVersion?: string;
  kubeProxyVersion?: string;
  operatingSystem?: string;
  architecture?: string;
}

interface PodInfo {
  name: string;
  namespace: string;
  phase?: string;
  creationTimestamp?: string;
}

interface ResourceAllocation {
  cpuRequests?: string;
  cpuLimits?: string;
  memoryRequests?: string;
  memoryLimits?: string;
}

interface NodeDetail {
  name: string;
  roles: string;
  labels: Record<string, string>;
  annotations: Record<string, string>;
  conditions: NodeCondition[];
  addresses: NodeAddress[];
  capacity: NodeCapacity;
  allocatable: NodeAllocatable;
  systemInfo: SystemInfo;
  pods: PodInfo[];
  allocatedResources: ResourceAllocation;
}

interface DataTableRowActionsProps<TData> {
  row: TData
}

export function DataTableRowActions<TData>({
  row,
}: DataTableRowActionsProps<TData>) {
  const [showLabelsModal, setShowLabelsModal] = useState(false)
  const [showAnnotationsModal, setShowAnnotationsModal] = useState(false)
  const [showNodeDetailsModal, setShowNodeDetailsModal] = useState(false)
  const [nodeDetail, setNodeDetail] = useState<NodeDetail | null>(null)
  const [loadingNodeDetail, setLoadingNodeDetail] = useState(false)
  
  // Function to fetch node details
  const fetchNodeDetails = async () => {
    setLoadingNodeDetail(true);
    try {
      // @ts-ignore - We know name exists on the row
      const nodeName = row.original?.name;

      const response = await axios_api.get(`/k8s/nodedetails?nodeName=${nodeName}`);

      if (response.status >= 200 && response.status < 300) {
        setNodeDetail(response.data);
        setShowNodeDetailsModal(true);
      } else {
        toast.error(`HTTP error! status: ${response.status}`);
      }
    } catch (err) {
      toast.error('Failed to fetch node details');
    } finally {
      setLoadingNodeDetail(false);
    }
  }

  // Function to handle node management actions
  const handleNodeAction = async (action: string) => {
    try {
      // @ts-ignore - We know name exists on the row
      const nodeName = row.original?.name;

      const response = await axios_api.post(`/k8s/${action}`, {
        nodeNames: [nodeName]
      });

      if (response.status >= 200 && response.status < 300) {
        toast.success(`Node ${nodeName} ${action} successfully`);
      } else {
        toast.error(`HTTP error! status: ${response.status}`);
      }
    } catch (err) {
    }
  }

  // @ts-ignore - We know these properties exist on the row
  const labels = row.original?.labels || {}
  // @ts-ignore - We know these properties exist on the row
  const annotations = row.original?.annotation || {}

  // Function to parse JSON string if needed
  const parseJsonIfString = (data: any) => {
    if (typeof data === 'string') {
      try {
        return JSON.parse(data)
      } catch (e) {
        return data
      }
    }
    return data
  }

  const parsedLabels = parseJsonIfString(labels)
  const parsedAnnotations = parseJsonIfString(annotations)

  // Function to copy content to clipboard
  const copyToClipboard = (content: string, type: string) => {
    navigator.clipboard.writeText(content)
      .then(() => toast.success(`${type} copied to clipboard`))
      .catch(() => toast.error(`Failed to copy ${type}`))
  }

  return (
    <>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button
            variant='ghost'
            className='data-[state=open]:bg-muted flex h-8 w-8 p-0'
          >
            <DotsHorizontalIcon className='h-4 w-4' />
            <span className='sr-only'>Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end' className='w-[160px]'>
          <DropdownMenuItem onClick={fetchNodeDetails} disabled={loadingNodeDetail}>
            {loadingNodeDetail ? 'Loading...' : 'Details'}
            <DropdownMenuShortcut>
              <IconInfoCircle size={16} />
            </DropdownMenuShortcut>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setShowLabelsModal(true)}>
            Show Labels
            <DropdownMenuShortcut>
              <IconLabel size={16} />
            </DropdownMenuShortcut>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setShowAnnotationsModal(true)}>
            Show Annotations
            <DropdownMenuShortcut>
              <IconTagFilled size={16} />
            </DropdownMenuShortcut>
          </DropdownMenuItem>
          <DropdownMenuSub>
            <DropdownMenuSubTrigger>
              Node Management
              <DropdownMenuShortcut>
                <IconSettings size={16} />
              </DropdownMenuShortcut>
            </DropdownMenuSubTrigger>
            <DropdownMenuSubContent>
              <DropdownMenuItem onClick={() => handleNodeAction('cordon')}>
                Cordon
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleNodeAction('uncordon')}>
                Uncordon
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleNodeAction('drain')}>
                Drain
              </DropdownMenuItem>
            </DropdownMenuSubContent>
          </DropdownMenuSub>
          <DropdownMenuSeparator />
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Labels Modal */}
      <Dialog open={showLabelsModal} onOpenChange={setShowLabelsModal}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              {/* @ts-ignore - We know name exists on the row */}
              <span>Labels for {row.original?.name}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(JSON.stringify(parsedLabels, null, 2), "Labels")}
                className="h-8 px-2"
              >
                <IconCopy size={16} />
              </Button>
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-auto max-h-[60vh]">
            {typeof parsedLabels === 'string' ? (
              <pre className="bg-muted p-4 rounded-md overflow-x-auto text-sm">
                {parsedLabels}
              </pre>
            ) : Object.keys(parsedLabels).length > 0 ? (
              <div className="space-y-4">
                <div className="flex flex-wrap gap-2 mb-4">
                  {Object.entries(parsedLabels).map(([key, value]) => (
                    <Badge key={key} variant="outline" className="px-2 py-1 text-xs">
                      {key}: {String(value)}
                    </Badge>
                  ))}
                </div>

                <div className="bg-muted rounded-md p-4">
                  <h3 className="text-sm font-medium mb-2">Details</h3>
                  <div className="grid grid-cols-1 gap-2">
                    {Object.entries(parsedLabels).map(([key, value]) => (
                      <div key={key} className="flex items-center border-b pb-2">
                        <span className="font-medium min-w-[120px] text-sm">{key}:</span>
                        <span className="text-muted-foreground text-sm overflow-hidden text-ellipsis">{String(value)}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-muted rounded-md p-4">
                  <h3 className="text-sm font-medium mb-2">JSON</h3>
                  <pre className="text-xs overflow-x-auto">
                    {JSON.stringify(parsedLabels, null, 2)}
                  </pre>
                </div>
              </div>
            ) : (
              <p className="text-muted-foreground">No labels found</p>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Annotations Modal */}
      <Dialog open={showAnnotationsModal} onOpenChange={setShowAnnotationsModal}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              {/* @ts-ignore - We know name exists on the row */}
              <span>Annotations for {row.original?.name}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(JSON.stringify(parsedAnnotations, null, 2), "Annotations")}
                className="h-8 px-2"
              >
                <IconCopy size={16} />
              </Button>
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-auto max-h-[60vh]">
            {typeof parsedAnnotations === 'string' ? (
              <pre className="bg-muted p-4 rounded-md overflow-x-auto text-sm">
                {parsedAnnotations}
              </pre>
            ) : Object.keys(parsedAnnotations).length > 0 ? (
              <div className="space-y-4">
                <div className="flex flex-wrap gap-2 mb-4">
                  {Object.entries(parsedAnnotations).map(([key, value]) => (
                    <Badge key={key} variant="outline" className="px-2 py-1 text-xs">
                      {key}: {String(value).substring(0, 20)}{String(value).length > 20 ? '...' : ''}
                    </Badge>
                  ))}
                </div>

                <div className="bg-muted rounded-md p-4">
                  <h3 className="text-sm font-medium mb-2">Details</h3>
                  <div className="grid grid-cols-1 gap-2">
                    {Object.entries(parsedAnnotations).map(([key, value]) => (
                      <div key={key} className="flex flex-col border-b pb-2">
                        <span className="font-medium text-sm">{key}:</span>
                        <span className="text-muted-foreground text-sm break-words">{String(value)}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-muted rounded-md p-4">
                  <h3 className="text-sm font-medium mb-2">JSON</h3>
                  <pre className="text-xs overflow-x-auto">
                    {JSON.stringify(parsedAnnotations, null, 2)}
                  </pre>
                </div>
              </div>
            ) : (
              <p className="text-muted-foreground">No annotations found</p>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Node Details Modal */}
      <Dialog open={showNodeDetailsModal} onOpenChange={setShowNodeDetailsModal}>
        <DialogContent className="max-w-[90vw] w-[1200px] max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>Node Details: {nodeDetail?.name}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(JSON.stringify(nodeDetail, null, 2), "Node Details")}
                className="h-8 px-2"
              >
                <IconCopy size={16} />
              </Button>
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-auto max-h-[75vh] space-y-6">
            {nodeDetail ? (
              <>
                {/* Basic Information */}
                <div className="bg-muted rounded-md p-4">
                  <h3 className="text-lg font-semibold mb-3">Basic Information</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="font-medium text-sm">Name:</span>
                      <span className="text-muted-foreground text-sm ml-2">{nodeDetail.name}</span>
                    </div>
                    <div>
                      <span className="font-medium text-sm">Roles:</span>
                      <span className="text-muted-foreground text-sm ml-2">{nodeDetail.roles}</span>
                    </div>
                  </div>
                </div>

                {/* System Information */}
                {nodeDetail.systemInfo && (
                  <div className="bg-muted rounded-md p-4">
                    <h3 className="text-lg font-semibold mb-3">System Information</h3>
                    <div className="grid grid-cols-2 gap-4">
                      {Object.entries(nodeDetail.systemInfo).map(([key, value]) => (
                        value && (
                          <div key={key}>
                            <span className="font-medium text-sm">{key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}:</span>
                            <span className="text-muted-foreground text-sm ml-2 break-all">{String(value)}</span>
                          </div>
                        )
                      ))}
                    </div>
                  </div>
                )}

                {/* Addresses */}
                {nodeDetail.addresses && nodeDetail.addresses.length > 0 && (
                  <div className="bg-muted rounded-md p-4">
                    <h3 className="text-lg font-semibold mb-3">Addresses</h3>
                    <div className="space-y-2">
                      {nodeDetail.addresses.map((address, index) => (
                        <div key={index} className="flex items-center">
                          <Badge variant="outline" className="mr-2">{address.type}</Badge>
                          <span className="text-sm">{address.address}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Capacity and Allocatable */}
                <div className="grid grid-cols-2 gap-4">
                  {nodeDetail.capacity && (
                    <div className="bg-muted rounded-md p-4">
                      <h3 className="text-lg font-semibold mb-3">Capacity</h3>
                      <div className="space-y-2">
                        {Object.entries(nodeDetail.capacity).map(([key, value]) => (
                          value && (
                            <div key={key} className="flex justify-between">
                              <span className="font-medium text-sm">{key}:</span>
                              <span className="text-muted-foreground text-sm">{String(value)}</span>
                            </div>
                          )
                        ))}
                      </div>
                    </div>
                  )}

                  {nodeDetail.allocatable && (
                    <div className="bg-muted rounded-md p-4">
                      <h3 className="text-lg font-semibold mb-3">Allocatable</h3>
                      <div className="space-y-2">
                        {Object.entries(nodeDetail.allocatable).map(([key, value]) => (
                          value && (
                            <div key={key} className="flex justify-between">
                              <span className="font-medium text-sm">{key}:</span>
                              <span className="text-muted-foreground text-sm">{String(value)}</span>
                            </div>
                          )
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Resource Allocation */}
                {nodeDetail.allocatedResources && (
                  <div className="bg-muted rounded-md p-4">
                    <h3 className="text-lg font-semibold mb-3">Allocated Resources</h3>
                    <div className="grid grid-cols-2 gap-4">
                      {Object.entries(nodeDetail.allocatedResources).map(([key, value]) => (
                        value && (
                          <div key={key} className="flex justify-between">
                            <span className="font-medium text-sm">{key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}:</span>
                            <span className="text-muted-foreground text-sm">{String(value)}</span>
                          </div>
                        )
                      ))}
                    </div>
                  </div>
                )}

                {/* Conditions */}
                {nodeDetail.conditions && nodeDetail.conditions.length > 0 && (
                  <div className="bg-muted rounded-md p-4">
                    <h3 className="text-lg font-semibold mb-3">Conditions</h3>
                    <div className="space-y-3">
                      {nodeDetail.conditions.map((condition, index) => (
                        <div key={index} className="border rounded p-3">
                          <div className="flex items-center justify-between mb-2">
                            <Badge variant={condition.status === 'True' ? 'default' : 'secondary'}>
                              {condition.type}
                            </Badge>
                            <span className="text-sm font-medium">{condition.status}</span>
                          </div>
                          {condition.reason && (
                            <div className="text-sm">
                              <span className="font-medium">Reason:</span> {condition.reason}
                            </div>
                          )}
                          {condition.message && (
                            <div className="text-sm text-muted-foreground mt-1">
                              {condition.message}
                            </div>
                          )}
                          <div className="flex gap-4 mt-2 text-xs text-muted-foreground">
                            {condition.lastHeartbeatTime && (
                              <span>Last Heartbeat: {new Date(condition.lastHeartbeatTime).toLocaleString()}</span>
                            )}
                            {condition.lastTransitionTime && (
                              <span>Last Transition: {new Date(condition.lastTransitionTime).toLocaleString()}</span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Pods */}
                {nodeDetail.pods && nodeDetail.pods.length > 0 && (
                  <div className="bg-muted rounded-md p-4">
                    <h3 className="text-lg font-semibold mb-3">Pods ({nodeDetail.pods.length})</h3>
                    <div className="max-h-60 overflow-y-auto space-y-2">
                      {nodeDetail.pods.map((pod, index) => (
                        <div key={index} className="flex items-center justify-between border rounded p-2">
                          <div>
                            <span className="font-medium text-sm">{pod.name}</span>
                            <span className="text-muted-foreground text-sm ml-2">({pod.namespace})</span>
                          </div>
                          <div className="flex items-center gap-2">
                            {pod.phase && <Badge variant="outline" className="text-xs">{pod.phase}</Badge>}
                            {pod.creationTimestamp && (
                              <span className="text-xs text-muted-foreground">
                                {new Date(pod.creationTimestamp).toLocaleDateString()}
                              </span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Labels */}
                {nodeDetail.labels && Object.keys(nodeDetail.labels).length > 0 && (
                  <div className="bg-muted rounded-md p-4">
                    <h3 className="text-lg font-semibold mb-3">Labels</h3>
                    <div className="flex flex-wrap gap-2">
                      {Object.entries(nodeDetail.labels).map(([key, value]) => (
                        <Badge key={key} variant="outline" className="text-xs">
                          {key}: {String(value)}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Annotations */}
                {nodeDetail.annotations && Object.keys(nodeDetail.annotations).length > 0 && (
                  <div className="bg-muted rounded-md p-4">
                    <h3 className="text-lg font-semibold mb-3">Annotations</h3>
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      {Object.entries(nodeDetail.annotations).map(([key, value]) => (
                        <div key={key} className="border-b pb-2">
                          <div className="font-medium text-sm break-all">{key}:</div>
                          <div className="text-muted-foreground text-sm break-all">{String(value)}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="flex items-center justify-center h-40">
                <p className="text-muted-foreground">No node details available</p>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
