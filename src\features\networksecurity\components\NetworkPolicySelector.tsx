import React, { useState, useEffect, useCallback } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import axios_api from '@/lib/axios_api';
import { toast } from 'sonner';

// Types
interface LabelData {
  key: string;
  value: string;
  hasNetworkPolicy?: boolean;
}

interface PodLabelResponse {
  pods: {
    name: string;
    labels: { [key: string]: string };
    hasNetworkPolicy?: boolean;
  }[];
}

interface NamespaceData {
  id: string;
  name: string;
  status: string;
  quota: string;
  createdAt: string;
  labels: string;
  annotation: string;
}

interface NetworkPolicySelectorProps {
  type: 'source' | 'destination';
  ruleType: 'ingress' | 'egress';
  currentValue: string;
  currentType: 'pod' | 'namespace' | 'external' | 'any';
  onChange: (value: string, type: 'pod' | 'namespace' | 'external' | 'any', namespace?: string) => void;
  disabled?: boolean;
}

const NetworkPolicySelector: React.FC<NetworkPolicySelectorProps> = ({
  type,
  ruleType,
  currentValue,
  currentType,
  onChange,
  disabled = false
}) => {
  const [selectorType, setSelectorType] = useState<'pod' | 'namespace' | 'cidr'>(
    currentType === 'external' ? 'cidr' : currentType === 'any' ? 'pod' : currentType
  );
  const [selectedNamespace, setSelectedNamespace] = useState('');
  const [selectedNamespaceLabel, setSelectedNamespaceLabel] = useState('');
  const [selectedPodLabel, setSelectedPodLabel] = useState('');
  const [customIP, setCustomIP] = useState('');
  const [initialized, setInitialized] = useState(false);

  const [namespaces, setNamespaces] = useState<NamespaceData[]>([]);
  const [namespaceLabels, setNamespaceLabels] = useState<LabelData[]>([]);
  const [podLabels, setPodLabels] = useState<LabelData[]>([]);

  // Determine if this field should be editable
  const isEditable = !disabled && (
    (type === 'source' && ruleType === 'ingress') ||
    (type === 'destination' && ruleType === 'egress')
  );

  // Initialize values from current props when editing starts
  useEffect(() => {
    if (isEditable && !initialized) {
      // Parse current value to set initial state
      if (currentType === 'external') {
        setSelectorType('cidr');
        setCustomIP(currentValue);
      } else if (currentType === 'namespace') {
        setSelectorType('namespace');
        // For namespace, currentValue is the namespace name
        setSelectedNamespace(currentValue);
      } else if (currentType === 'pod') {
        setSelectorType('pod');
        setSelectedPodLabel(currentValue);
      }
      setInitialized(true);
    }

    // Reset when switching to non-editable
    if (!isEditable) {
      setInitialized(false);
    }
  }, [isEditable, initialized]);

  // Update values when currentValue or currentType changes (for external updates)
  useEffect(() => {
    if (!isEditable) {
      // Reset all values when not editable
      setSelectedNamespace('');
      setSelectedNamespaceLabel('');
      setSelectedPodLabel('');
      setCustomIP('');
      setSelectorType(currentType === 'external' ? 'cidr' : currentType === 'any' ? 'pod' : currentType);
    } else if (isEditable && initialized) {
      // Update values when props change during editing
      if (currentType === 'external') {
        setSelectorType('cidr');
        setCustomIP(currentValue);
      } else if (currentType === 'namespace') {
        setSelectorType('namespace');
        setSelectedNamespace(currentValue);
      } else if (currentType === 'pod') {
        setSelectorType('pod');
        setSelectedPodLabel(currentValue);
      }
    }
  }, [isEditable, currentValue, currentType, initialized]);

  // Fetch namespaces
  useEffect(() => {
    const fetchNamespaces = async () => {
      try {
        const response = await axios_api.get<NamespaceData[]>("/k8s/namespaces");
        setNamespaces(response.data);
      } catch (error) {
        console.error("Error fetching namespaces:", error);
        toast.error("Failed to fetch namespaces.");
      }
    };

    if (isEditable) {
      fetchNamespaces();
    }
  }, [isEditable]);

  // Fetch namespace labels
  useEffect(() => {
    const fetchNamespaceLabels = async () => {
      if (selectorType === 'namespace' && selectedNamespace) {
        try {
          const selectedNamespaceData = namespaces.find((ns) => ns.name === selectedNamespace);
          
          if (selectedNamespaceData && selectedNamespaceData.labels) {
            try {
              const labelsObject = JSON.parse(selectedNamespaceData.labels);
              const labelArray: LabelData[] = Object.entries(labelsObject).map(([key, value]) => ({ 
                key, 
                value: String(value), 
                hasNetworkPolicy: false 
              }));
              setNamespaceLabels(labelArray);
            } catch (error) {
              console.error("Error parsing namespace labels:", error);
              setNamespaceLabels([]);
            }
          } else {
            setNamespaceLabels([]);
          }
        } catch (error) {
          console.error("Error fetching namespace labels:", error);
          setNamespaceLabels([]);
        }
      } else {
        setNamespaceLabels([]);
      }
    };

    fetchNamespaceLabels();
  }, [selectorType, selectedNamespace, namespaces]);

  // Fetch pod labels
  useEffect(() => {
    const fetchPodLabels = async () => {
      if (selectorType === 'pod' && selectedNamespace) {
        try {
          const response = await axios_api.get<PodLabelResponse>(`/k8s/pod-labels`, {
            params: { namespace: selectedNamespace }
          });

          const allLabels: { key: string; value: string }[] = [];
          
          response.data.pods.forEach((pod) => {
            for (const key in pod.labels) {
              if (pod.labels.hasOwnProperty(key)) {
                allLabels.push({ key: key, value: pod.labels[key] });
              }
            }
          });

          // Remove duplicates by creating a unique set
          const uniqueLabels = Array.from(
            new Map(allLabels.map(label => [`${label.key}=${label.value}`, label])).values()
          );

          const labelData: LabelData[] = uniqueLabels.map((label) => ({
            key: label.key,
            value: label.value,
            hasNetworkPolicy: false,
          }));

          setPodLabels(labelData);
        } catch (error) {
          console.error("Error fetching pod labels:", error);
          setPodLabels([]);
        }
      } else {
        setPodLabels([]);
      }
    };

    fetchPodLabels();
  }, [selectorType, selectedNamespace]);

  // Handle individual field changes
  const handleSelectorTypeChange = (value: 'pod' | 'namespace' | 'cidr') => {
    setSelectorType(value);
    // Reset other fields when type changes
    setSelectedNamespace('');
    setSelectedNamespaceLabel('');
    setSelectedPodLabel('');
    setCustomIP('');
  };

  const handleNamespaceChange = (value: string) => {
    setSelectedNamespace(value);
    setSelectedNamespaceLabel('');
    setSelectedPodLabel('');
  };

  const handleNamespaceLabelChange = (value: string) => {
    setSelectedNamespaceLabel(value);
    if (selectedNamespace && value) {
      // For namespace, we need to pass the namespace name, not the label value
      onChange(selectedNamespace, 'namespace');
    }
  };

  const handlePodLabelChange = (value: string) => {
    setSelectedPodLabel(value);
    if (selectedNamespace && value) {
      // For pod, we pass just the label value (not key=value format)
      onChange(value, 'pod', selectedNamespace);
    }
  };

  const handleCustomIPChange = (value: string) => {
    setCustomIP(value);
    if (value) {
      onChange(value, 'external');
    }
  };

  if (!isEditable) {
    return (
      <div className="space-y-1">
        <div className="flex items-center gap-2">
          <div className={`h-2 w-2 rounded-full ${
            currentType === 'external' ? 'bg-red-500' :
            currentType === 'pod' ? 'bg-blue-500' :
            currentType === 'namespace' ? 'bg-purple-500' :
            'bg-gray-500'
          }`} />
          <span className="text-sm font-mono">{currentValue}</span>
        </div>
        <div className="text-xs text-muted-foreground capitalize">
          {currentType}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div>
        <Label className="text-xs">Type</Label>
        <Select value={selectorType} onValueChange={handleSelectorTypeChange}>
          <SelectTrigger className="w-full h-6 text-xs">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="pod">Pod</SelectItem>
            <SelectItem value="namespace">Namespace</SelectItem>
            <SelectItem value="cidr">CIDR</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {(selectorType === 'pod' || selectorType === 'namespace') && (
        <div>
          <Label className="text-xs">Namespace</Label>
          <Select value={selectedNamespace} onValueChange={handleNamespaceChange}>
            <SelectTrigger className="w-full h-6 text-xs">
              <SelectValue placeholder="Select namespace" />
            </SelectTrigger>
            <SelectContent>
              {namespaces.map((ns) => (
                <SelectItem key={ns.id} value={ns.name}>
                  {ns.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {selectorType === 'namespace' && selectedNamespace && (
        <div>
          <Label className="text-xs">Namespace Label</Label>
          <Select value={selectedNamespaceLabel} onValueChange={handleNamespaceLabelChange}>
            <SelectTrigger className="w-full h-6 text-xs">
              <SelectValue placeholder="Select label" />
            </SelectTrigger>
            <SelectContent>
              {namespaceLabels.map((label) => (
                <SelectItem key={label.key} value={label.value}>
                  {label.key}: {label.value}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {selectorType === 'pod' && selectedNamespace && (
        <div>
          <Label className="text-xs">Pod Label</Label>
          <Select value={selectedPodLabel} onValueChange={handlePodLabelChange}>
            <SelectTrigger className="w-full h-6 text-xs">
              <SelectValue placeholder="Select pod label" />
            </SelectTrigger>
            <SelectContent>
              {podLabels.map((label, index) => (
                <SelectItem key={`${label.key}-${label.value}-${index}`} value={label.value}>
                  {label.key}: {label.value}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {selectorType === 'cidr' && (
        <div>
          <Label className="text-xs">IP/CIDR</Label>
          <Input
            value={customIP}
            onChange={(e) => handleCustomIPChange(e.target.value)}
            className="text-sm h-6"
            placeholder="e.g., 10.0.0.0/8, ***********/24"
          />
          <div className="text-xs text-muted-foreground mt-1">
            Enter a valid CIDR notation (IP/subnet)
          </div>
        </div>
      )}
    </div>
  );
};

export default NetworkPolicySelector;
