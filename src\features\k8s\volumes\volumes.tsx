import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import axios_api from '@/lib/axios_api';
import { DataTable } from '../components/data-table';

import { Header } from '@/components/layout/header';
import { ThemeSwitch } from '@/components/theme-switch';
import { ProfileDropdown } from '@/components/profile-dropdown';
import { Search } from '@/components/search';
import { Main } from '@/components/layout/main';
import { columns } from './components/columns';

const Volumes: React.FC = () => {
  const [volumes, setVolumes] = useState<[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchVolumes = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axios_api.get('/k8s/persistentvolumes');
      if (response.status >= 200 && response.status < 300) {
        if (response?.data) {
          setVolumes(response.data);
          toast.success('Volumes loaded successfully');
        } else {
          toast.error('Response data missing or in incorrect format.');
        }
      } else {
        toast.error(`HTTP error! status: ${response.status}`);
      }
    } catch (err) {
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchVolumes();
  }, [fetchVolumes]);

  return (
    <>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>
      <Main fixed>
        <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
          <DataTable 
            data={volumes} 
            columns={columns} 
            loading={loading}
            title="Persistent Volumes"
            description="List of all Kubernetes persistent volumes in the cluster"
          />
        </div>
      </Main>
    </>
  );
};

export default Volumes;

