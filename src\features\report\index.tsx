import { useEffect, useState, useRef } from "react";
import axios_api from "@/lib/axios_api";
import { saveAs } from "file-saver";
import * as d3 from "d3";

const entities = [
  "configmaps",
  "cronjobs",
  "daemonsets",
  "deployments",
  "ingresses",
  "namespaces",
  "nodes",
  "persistentvolumeclaims",
  "persistentvolumes",
  "pods",
  "secrets",
  "services",
  "vuln_result",
];

type ChartDatum = {
  name: string;
  value: number;
};

function ReportK8S() {
  const [data, setData] = useState<Record<string, any>>({});
  const pieChartRef = useRef<HTMLDivElement>(null);
  const barChartRef = useRef<HTMLDivElement>(null);
  // const lineChartRef = useRef<HTMLDivElement>(null); // Not used

  useEffect(() => {
    entities.forEach((entity) => {
      axios_api
        .get(`/k8s/report/${entity}`)
        .then((res) => {
          setData((prev) => ({ ...prev, [entity]: res.data }));
        })
        .catch((err) => {
          console.error(`Failed to fetch ${entity}`, err);
        });
    });
  }, []);

  useEffect(() => {
    if (Object.keys(data).length === 0) return;

    if (pieChartRef.current) d3.select(pieChartRef.current).selectAll("*").remove();
    if (barChartRef.current) d3.select(barChartRef.current).selectAll("*").remove();

    const chartData: ChartDatum[] = entities.map((e) => ({
      name: e.replace(/_/g, " "),
      value: Array.isArray(data[e]) ? data[e].length : 0,
    }));

    // === PIE CHART ===
    const width = 400;
    const height = 400;
    const radius = Math.min(width, height) / 2;

    const color = d3
      .scaleOrdinal<string>()
      .domain(chartData.map(d => d.name))
      .range([
        "#4F46E5", "#10B981", "#F59E0B", "#EF4444", "#6366F1",
        "#8B5CF6", "#3B82F6", "#06B6D4", "#14B8A6", "#A855F7",
        "#EC4899", "#F97316", "#84CC16"
      ]);

    const pie = d3.pie<ChartDatum>().value((d) => d.value);

    const arc = d3.arc<d3.PieArcDatum<ChartDatum>>()
      .innerRadius(0)
      .outerRadius(radius);

    const pieSvg = d3
      .select(pieChartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${width / 2},${height / 2})`);

    const arcs = pieSvg
      .selectAll("arc")
      .data(pie(chartData))
      .enter()
      .append("g");

    arcs
      .append("path")
      .attr("fill", (d) => color(d.data.name))
      .transition()
      .duration(1000)
      .attrTween("d", function (d) {
        const i = d3.interpolate({ startAngle: 0, endAngle: 0 }, d);
        return (t) => arc(i(t)) || "";
      });

    // === BAR CHART ===
    const margin = { top: 20, right: 20, bottom: 60, left: 40 };
    const barWidth = 600 - margin.left - margin.right;
    const barHeight = 400 - margin.top - margin.bottom;

    const x = d3
      .scaleBand<string>()
      .range([0, barWidth])
      .padding(0.1)
      .domain(chartData.map((d) => d.name));

    const y = d3
      .scaleLinear()
      .range([barHeight, 0])
      .domain([0, d3.max(chartData, (d) => d.value) || 0]);

    const barSvg = d3
      .select(barChartRef.current)
      .append("svg")
      .attr("width", barWidth + margin.left + margin.right)
      .attr("height", barHeight + margin.top + margin.bottom)
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    barSvg
      .selectAll(".bar")
      .data(chartData)
      .enter()
      .append("rect")
      .attr("class", "bar")
      .attr("x", (d) => x(d.name)!)
      .attr("width", x.bandwidth())
      .attr("y", barHeight)
      .attr("height", 0)
      .attr("fill", (d) => color(d.name))
      .transition()
      .duration(1000)
      .attr("y", (d) => y(d.value))
      .attr("height", (d) => barHeight - y(d.value));

    // X Axis
    barSvg
      .append("g")
      .attr("transform", `translate(0,${barHeight})`)
      .call(d3.axisBottom(x))
      .selectAll("text")
      .attr("transform", "rotate(-45)")
      .style("text-anchor", "end");

    // Y Axis
    barSvg.append("g").call(d3.axisLeft(y));
  }, [data]);

  const downloadExcel = () => {
    axios_api
      .get("/k8s/report/export-all", { responseType: "blob" })
      .then((res) => saveAs(res.data, "kubernetes-report.xlsx"))
      .catch((err) => console.error("Export failed", err));
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="max-w-7xl mx-auto bg-white shadow-lg rounded-2xl p-8">
        <div className="mb-8 flex justify-between items-center">
          <h2 className="text-xl font-semibold">Reports</h2>
          <button
            onClick={downloadExcel}
            className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
          >
            Export to excel
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
          <div className="bg-white p-4 shadow rounded-lg">
            <div ref={pieChartRef}></div>
          </div>
          <div className="bg-white p-4 shadow rounded-lg overflow-x-auto">
            <div ref={barChartRef}></div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ReportK8S;
