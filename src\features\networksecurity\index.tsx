import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { <PERSON><PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Header } from '@/components/layout/header';
import { Main } from '@/components/layout/main';
import { ProfileDropdown } from '@/components/profile-dropdown';
import { Search } from '@/components/search';
import { ThemeSwitch } from '@/components/theme-switch';
import SecurityPolicyRule from './securitypolicyrule/securitypolicyrule';
import { MonitorTab } from './monitor/monitor';
import NetworkDiagram from './networkdiagram/networkdiagram';
import { IconActivity, IconNetwork, IconShield } from '@tabler/icons-react';
import NetworkDiagramV2 from './networkdiagramv2/networkdiagramv2';

export default function NetworkSecurity() {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('monitor');

  return (
    <>
      <Header fixed>
        <Search />
        <div className="ml-auto flex items-center space-x-4">
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main >
        <div className="w-full mx-auto py-1">
          <h1 className="text-2xl font-bold mb-4">Network Security</h1>
          <Tabs defaultValue="monitor" value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="flex w-full p-1 bg-muted rounded-lg mb-6">
              <TabsTrigger 
                value="monitor" 
                className="flex-1 flex items-center justify-center gap-2 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow transition-all"
              >
                <IconActivity size={18} />
                <span>Monitor</span>
              </TabsTrigger>
              <TabsTrigger 
                value="diagram" 
                className="flex-1 flex items-center justify-center gap-2 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow transition-all"
              >
                <IconNetwork size={18} />
                <span>Network Diagram</span>
              </TabsTrigger>

<TabsTrigger 
                value="diagramv2" 
                className="flex-1 flex items-center justify-center gap-2 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow transition-all"
              >
                <IconNetwork size={18} />
                <span>Network Diagram V2</span>
              </TabsTrigger>

              <TabsTrigger 
                value="policy" 
                className="flex-1 flex items-center justify-center gap-2 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow transition-all"
              >
                <IconShield size={18} />
                <span>Network Policy</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="monitor" className="w-full h-full">
              <MonitorTab data={data} loading={loading} setData={setData} setLoading={setLoading} />
            </TabsContent>

            <TabsContent value="diagram">
              <Card>
                <CardContent className="pt-6">
                  <div className="min-h-[400px] flex items-center justify-center">
                    <NetworkDiagram/>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="diagramv2">
              <Card>
                <CardContent className="pt-6">
                  <div className="min-h-[400px] flex items-center justify-center">
                    <NetworkDiagramV2/>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="policy">
              <Card>
                <CardContent className="pt-6">
                  <div className="min-h-[400px] flex items-center justify-center">
                    <SecurityPolicyRule/>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </Main>
    </>
  );
}
