import { Checkbox } from '@/components/ui/checkbox'
import { ColumnDef } from '@tanstack/react-table';
import { DataTableRowActions } from './data-table-row-actions';
import { DataTableColumnHeader } from '../../components/data-table-column-header';
import { formatDateToLocal } from '@/lib/dateformat';



interface DataEntity {
  id?: string;
  name?: string;
  namespace?: string;
  createdAt?: string;
  lastSeenAt?: string;
  labels?: Record<string, string>;
  annotation?: Record<string, string>;
  capacity?: string;
  accessModes?: string;
  reclaimPolicy?: string;
  status?: string;
  claim?: string;
  storageClass?: string;
}




export const columns: ColumnDef<DataEntity>[] = [
   {
    id: 'select',
    header: () => null, // Remove header checkbox for multiple selection
    cell: ({ row, table }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => {
          // Deselect all rows first
          table.toggleAllRowsSelected(false)
          // Then select only this row
          row.toggleSelected(!!value)
        }}
        aria-label='Select row'
        className='translate-y-[2px]'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Name' />
    ),
    cell: ({ row }) => <div >{row.getValue('name')}</div>,
    enableSorting: true,
    enableHiding: false,
  },
  {
    accessorKey: 'capacity',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Capacity' />
    ),
    cell: ({ row }) => <div >{row.getValue('capacity')} Gi</div>,
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'accessModes',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Access Modes' />
    ),
    cell: ({ row }) => <div >{row.getValue('accessModes')}</div>,
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'reclaimPolicy',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Reclaim Policy' />
    ),
    cell: ({ row }) => <div >{row.getValue('reclaimPolicy')}</div>,
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Status' />
    ),
    cell: ({ row }) => <div >{row.getValue('status')}</div>,
    enableSorting: true,
    enableHiding: false,
  },
  {
    accessorKey: 'claim',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Claim' />
    ),
    cell: ({ row }) => <div >{row.getValue('claim')}</div>,
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'storageClass',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Storage Class' />
    ),
    cell: ({ row }) => <div >{row.getValue('storageClass')}</div>,
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Created At' />
    ),
    cell: ({ row }) => <div >{formatDateToLocal(row.getValue('createdAt'))}</div>,
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'lastSeenAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Last Seen At' />
    ),
    cell: ({ row }) => <div>{formatDateToLocal(row.getValue('lastSeenAt'))}</div>,
    enableSorting: true,
    enableHiding: true,
  },
  {
    id: 'actions',
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
];

