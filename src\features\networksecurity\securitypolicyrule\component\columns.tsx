import { ColumnDef } from "@tanstack/react-table"
import { DataTableColumnHeader } from "../../component/data-table-column-header"
import { Checkbox } from "@/components/ui/checkbox"
import { DataTableRowActions } from "./data-table-row-actions"
import { Badge } from "@/components/ui/badge"

export type DataEntity = {
    name: string;
    namespace: string;
    creationTimestamp: string;
    podSelector: any;
    policyTypes: string[];
}

export const columns = (onRefresh: () => Promise<any>): ColumnDef<DataEntity>[] => [
   {
    id: 'select',
    header: () => null, // Remove header checkbox for multiple selection
    cell: ({ row, table }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => {
          // Deselect all rows first
          table.toggleAllRowsSelected(false)
          // Then select only this row
          row.toggleSelected(!!value)
        }}
        aria-label='Select row'
        className='translate-y-[2px]'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
    cell: ({ row }) => {
      return <div className="font-medium">{row.getValue("name")}</div>
    },
  },
  {
    accessorKey: "namespace",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Namespace" />
    ),
    cell: ({ row }) => {
      return <div>{row.getValue("namespace")}</div>
    },
  },
  {
    accessorKey: "creationTimestamp",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created" />
    ),
    cell: ({ row }) => {
      const timestamp = row.getValue("creationTimestamp") as string;
      // Format the timestamp
      const date = new Date(timestamp);
      const formattedDate = date.toLocaleDateString();
      return <div>{formattedDate}</div>
    },
  },
  {
    accessorKey: "podSelector",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Pod Selector" />
    ),
    cell: ({ row }) => {
      const podSelector = row.getValue("podSelector") as any;
      if (!podSelector || !podSelector.matchLabels) {
        return <div className="text-muted-foreground italic">None</div>;
      }
      
      return (
        <div className="flex flex-wrap gap-1">
          {Object.entries(podSelector.matchLabels).map(([key, value]) => (
            <Badge key={key} variant="outline" className="text-xs">
              {key}: {value as string}
            </Badge>
          ))}
        </div>
      );
    },
  },
  {
    accessorKey: "policyTypes",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Policy Types" />
    ),
    cell: ({ row }) => {
      const policyTypes = row.getValue("policyTypes") as string[];
      if (!policyTypes || policyTypes.length === 0) {
        return <div className="text-muted-foreground italic">None</div>;
      }
      
      return (
        <div className="flex flex-wrap gap-1">
          {policyTypes.map((type) => (
            <Badge key={type} variant="secondary" className="text-xs">
              {type}
            </Badge>
          ))}
        </div>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => <DataTableRowActions row={row} onRefresh={onRefresh} />,
  },
];




