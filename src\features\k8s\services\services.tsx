import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import axios_api from '@/lib/axios_api';
import { DataTable } from '../components/data-table';

import { Header } from '@/components/layout/header';
import { ThemeSwitch } from '@/components/theme-switch';
import { ProfileDropdown } from '@/components/profile-dropdown';
import { Search } from '@/components/search';
import { Main } from '@/components/layout/main';
import { Loader2 } from 'lucide-react';
import { columns } from './components/columns';

const Services: React.FC = () => {

  const [services, setServices] = useState<[]>([]);
  const [loading, setLoading] = useState(true);


  const fetchServices = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axios_api.get('/k8s/services');
      if (response.status >= 200 && response.status < 300) {
        if (response?.data) {
          setServices(response.data);
          toast.success('services loaded successfully');
        } else {
          toast.error('Response data missing or in incorrect format.');
        }
      } else {
        toast.error(`HTTP error! status: ${response.status}`);
      }
    } catch (err) {
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchServices();
  }, [fetchServices]);


  return (

    <>
      {/* ===== Top Heading ===== */}
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>
      <Main fixed>
        <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading services...</span>
            </div>
          ) : (
            <DataTable data={services} columns={columns} />
          )}
        </div>
      </Main>
    </>
  );
};

export default Services;



