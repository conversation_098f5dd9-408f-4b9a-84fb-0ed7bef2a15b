import { useAuthStore } from '@/stores/authStore';
import axios from 'axios';
import { toast } from 'sonner';

// Axios instance oluştur
const axios_api = axios.create({
    baseURL: '/api', // Backend API URL
});

// Her istek öncesinde token ekleyen interceptor
axios_api.interceptors.request.use(
    (config) => {
        // Get token directly from the store instead of using the hook
        const accessToken = useAuthStore.getState().auth.accessToken;
        if (accessToken) {
            config.headers.Authorization = `Bearer ${accessToken}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

axios_api.interceptors.response.use(
  (response) => response,
  (error) => {
    let message = 'An error occurred';

    if (error.response) {
      const status = error.response.status;
      const serverMessage = error.response.data?.message;

      message = serverMessage
        ? `[${status}] ${serverMessage}`
        : `[${status}] A server error occurred.`;

      toast.error(message);
    } else if (error.request) {
      message = 'No response received from the server.';
      toast.error(message);
    } else {
      message = 'An error occurred while setting up the request.';
      toast.error(message);
    }

    return Promise.reject(error);
  }
);

export default axios_api;
