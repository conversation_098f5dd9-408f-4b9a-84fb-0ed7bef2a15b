/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated/route'
import { Route as AuthenticatedIndexImport } from './routes/_authenticated/index'
import { Route as errors503Import } from './routes/(errors)/503'
import { Route as errors500Import } from './routes/(errors)/500'
import { Route as errors404Import } from './routes/(errors)/404'
import { Route as errors403Import } from './routes/(errors)/403'
import { Route as errors401Import } from './routes/(errors)/401'
import { Route as authSignIn2Import } from './routes/(auth)/sign-in-2'
import { Route as authSignInImport } from './routes/(auth)/sign-in'
import { Route as AuthenticatedSettingsRouteImport } from './routes/_authenticated/settings/route'
import { Route as AuthenticatedVulnerabilityIndexImport } from './routes/_authenticated/vulnerability/index'
import { Route as AuthenticatedUsersIndexImport } from './routes/_authenticated/users/index'
import { Route as AuthenticatedTasksIndexImport } from './routes/_authenticated/tasks/index'
import { Route as AuthenticatedSettingsIndexImport } from './routes/_authenticated/settings/index'
import { Route as AuthenticatedRolesIndexImport } from './routes/_authenticated/roles/index'
import { Route as AuthenticatedNetworksecurityIndexImport } from './routes/_authenticated/networksecurity/index'
import { Route as AuthenticatedK8sIndexImport } from './routes/_authenticated/k8s/index'
import { Route as AuthenticatedHelpCenterIndexImport } from './routes/_authenticated/help-center/index'
import { Route as AuthenticatedChatsIndexImport } from './routes/_authenticated/chats/index'
import { Route as AuthenticatedBackupIndexImport } from './routes/_authenticated/backup/index'
import { Route as AuthenticatedAppsIndexImport } from './routes/_authenticated/apps/index'
import { Route as AuthenticatedSettingsNotificationsImport } from './routes/_authenticated/settings/notifications'
import { Route as AuthenticatedSettingsDisplayImport } from './routes/_authenticated/settings/display'
import { Route as AuthenticatedSettingsAppearanceImport } from './routes/_authenticated/settings/appearance'
import { Route as AuthenticatedSettingsAccountImport } from './routes/_authenticated/settings/account'
import { Route as AuthenticatedK8sgraphK8sgraphImport } from './routes/_authenticated/k8sgraph/k8sgraph'
import { Route as AuthenticatedK8sVolumesclaimsImport } from './routes/_authenticated/k8s/volumes_claims'
import { Route as AuthenticatedK8sVolumesImport } from './routes/_authenticated/k8s/volumes'
import { Route as AuthenticatedK8sServicesImport } from './routes/_authenticated/k8s/services'
import { Route as AuthenticatedK8sSecretsImport } from './routes/_authenticated/k8s/secrets'
import { Route as AuthenticatedK8sPodsImport } from './routes/_authenticated/k8s/pods'
import { Route as AuthenticatedK8sNodesImport } from './routes/_authenticated/k8s/nodes'
import { Route as AuthenticatedK8sNamespacesImport } from './routes/_authenticated/k8s/namespaces'
import { Route as AuthenticatedK8sIngressesImport } from './routes/_authenticated/k8s/ingresses'
import { Route as AuthenticatedK8sDeploymentsImport } from './routes/_authenticated/k8s/deployments'
import { Route as AuthenticatedK8sDaemonsetsImport } from './routes/_authenticated/k8s/daemonsets'
import { Route as AuthenticatedK8sCronjobsImport } from './routes/_authenticated/k8s/cronjobs'
import { Route as AuthenticatedK8sConfigmapsImport } from './routes/_authenticated/k8s/configmaps'

// Create/Update Routes

const AuthenticatedRouteRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedIndexRoute = AuthenticatedIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const errors503Route = errors503Import.update({
  id: '/(errors)/503',
  path: '/503',
  getParentRoute: () => rootRoute,
} as any)

const errors500Route = errors500Import.update({
  id: '/(errors)/500',
  path: '/500',
  getParentRoute: () => rootRoute,
} as any)

const errors404Route = errors404Import.update({
  id: '/(errors)/404',
  path: '/404',
  getParentRoute: () => rootRoute,
} as any)

const errors403Route = errors403Import.update({
  id: '/(errors)/403',
  path: '/403',
  getParentRoute: () => rootRoute,
} as any)

const errors401Route = errors401Import.update({
  id: '/(errors)/401',
  path: '/401',
  getParentRoute: () => rootRoute,
} as any)

const authSignIn2Route = authSignIn2Import.update({
  id: '/(auth)/sign-in-2',
  path: '/sign-in-2',
  getParentRoute: () => rootRoute,
} as any)

const authSignInRoute = authSignInImport.update({
  id: '/(auth)/sign-in',
  path: '/sign-in',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedSettingsRouteRoute = AuthenticatedSettingsRouteImport.update(
  {
    id: '/settings',
    path: '/settings',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any,
)

const AuthenticatedVulnerabilityIndexRoute =
  AuthenticatedVulnerabilityIndexImport.update({
    id: '/vulnerability/',
    path: '/vulnerability/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedUsersIndexRoute = AuthenticatedUsersIndexImport.update({
  id: '/users/',
  path: '/users/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedTasksIndexRoute = AuthenticatedTasksIndexImport.update({
  id: '/tasks/',
  path: '/tasks/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedSettingsIndexRoute = AuthenticatedSettingsIndexImport.update(
  {
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any,
)

const AuthenticatedRolesIndexRoute = AuthenticatedRolesIndexImport.update({
  id: '/roles/',
  path: '/roles/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedNetworksecurityIndexRoute =
  AuthenticatedNetworksecurityIndexImport.update({
    id: '/networksecurity/',
    path: '/networksecurity/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedK8sIndexRoute = AuthenticatedK8sIndexImport.update({
  id: '/k8s/',
  path: '/k8s/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedHelpCenterIndexRoute =
  AuthenticatedHelpCenterIndexImport.update({
    id: '/help-center/',
    path: '/help-center/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedChatsIndexRoute = AuthenticatedChatsIndexImport.update({
  id: '/chats/',
  path: '/chats/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedBackupIndexRoute = AuthenticatedBackupIndexImport.update({
  id: '/backup/',
  path: '/backup/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedAppsIndexRoute = AuthenticatedAppsIndexImport.update({
  id: '/apps/',
  path: '/apps/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedSettingsNotificationsRoute =
  AuthenticatedSettingsNotificationsImport.update({
    id: '/notifications',
    path: '/notifications',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedSettingsDisplayRoute =
  AuthenticatedSettingsDisplayImport.update({
    id: '/display',
    path: '/display',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedSettingsAppearanceRoute =
  AuthenticatedSettingsAppearanceImport.update({
    id: '/appearance',
    path: '/appearance',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedSettingsAccountRoute =
  AuthenticatedSettingsAccountImport.update({
    id: '/account',
    path: '/account',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedK8sgraphK8sgraphRoute =
  AuthenticatedK8sgraphK8sgraphImport.update({
    id: '/k8sgraph/k8sgraph',
    path: '/k8sgraph/k8sgraph',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedK8sVolumesclaimsRoute =
  AuthenticatedK8sVolumesclaimsImport.update({
    id: '/k8s/volumes_claims',
    path: '/k8s/volumes_claims',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedK8sVolumesRoute = AuthenticatedK8sVolumesImport.update({
  id: '/k8s/volumes',
  path: '/k8s/volumes',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedK8sServicesRoute = AuthenticatedK8sServicesImport.update({
  id: '/k8s/services',
  path: '/k8s/services',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedK8sSecretsRoute = AuthenticatedK8sSecretsImport.update({
  id: '/k8s/secrets',
  path: '/k8s/secrets',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedK8sPodsRoute = AuthenticatedK8sPodsImport.update({
  id: '/k8s/pods',
  path: '/k8s/pods',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedK8sNodesRoute = AuthenticatedK8sNodesImport.update({
  id: '/k8s/nodes',
  path: '/k8s/nodes',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedK8sNamespacesRoute = AuthenticatedK8sNamespacesImport.update(
  {
    id: '/k8s/namespaces',
    path: '/k8s/namespaces',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any,
)

const AuthenticatedK8sIngressesRoute = AuthenticatedK8sIngressesImport.update({
  id: '/k8s/ingresses',
  path: '/k8s/ingresses',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedK8sDeploymentsRoute =
  AuthenticatedK8sDeploymentsImport.update({
    id: '/k8s/deployments',
    path: '/k8s/deployments',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedK8sDaemonsetsRoute = AuthenticatedK8sDaemonsetsImport.update(
  {
    id: '/k8s/daemonsets',
    path: '/k8s/daemonsets',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any,
)

const AuthenticatedK8sCronjobsRoute = AuthenticatedK8sCronjobsImport.update({
  id: '/k8s/cronjobs',
  path: '/k8s/cronjobs',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedK8sConfigmapsRoute = AuthenticatedK8sConfigmapsImport.update(
  {
    id: '/k8s/configmaps',
    path: '/k8s/configmaps',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any,
)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRoute
    }
    '/_authenticated/settings': {
      id: '/_authenticated/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof AuthenticatedSettingsRouteImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/(auth)/sign-in': {
      id: '/(auth)/sign-in'
      path: '/sign-in'
      fullPath: '/sign-in'
      preLoaderRoute: typeof authSignInImport
      parentRoute: typeof rootRoute
    }
    '/(auth)/sign-in-2': {
      id: '/(auth)/sign-in-2'
      path: '/sign-in-2'
      fullPath: '/sign-in-2'
      preLoaderRoute: typeof authSignIn2Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/401': {
      id: '/(errors)/401'
      path: '/401'
      fullPath: '/401'
      preLoaderRoute: typeof errors401Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/403': {
      id: '/(errors)/403'
      path: '/403'
      fullPath: '/403'
      preLoaderRoute: typeof errors403Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/404': {
      id: '/(errors)/404'
      path: '/404'
      fullPath: '/404'
      preLoaderRoute: typeof errors404Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/500': {
      id: '/(errors)/500'
      path: '/500'
      fullPath: '/500'
      preLoaderRoute: typeof errors500Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/503': {
      id: '/(errors)/503'
      path: '/503'
      fullPath: '/503'
      preLoaderRoute: typeof errors503Import
      parentRoute: typeof rootRoute
    }
    '/_authenticated/': {
      id: '/_authenticated/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/k8s/configmaps': {
      id: '/_authenticated/k8s/configmaps'
      path: '/k8s/configmaps'
      fullPath: '/k8s/configmaps'
      preLoaderRoute: typeof AuthenticatedK8sConfigmapsImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/k8s/cronjobs': {
      id: '/_authenticated/k8s/cronjobs'
      path: '/k8s/cronjobs'
      fullPath: '/k8s/cronjobs'
      preLoaderRoute: typeof AuthenticatedK8sCronjobsImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/k8s/daemonsets': {
      id: '/_authenticated/k8s/daemonsets'
      path: '/k8s/daemonsets'
      fullPath: '/k8s/daemonsets'
      preLoaderRoute: typeof AuthenticatedK8sDaemonsetsImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/k8s/deployments': {
      id: '/_authenticated/k8s/deployments'
      path: '/k8s/deployments'
      fullPath: '/k8s/deployments'
      preLoaderRoute: typeof AuthenticatedK8sDeploymentsImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/k8s/ingresses': {
      id: '/_authenticated/k8s/ingresses'
      path: '/k8s/ingresses'
      fullPath: '/k8s/ingresses'
      preLoaderRoute: typeof AuthenticatedK8sIngressesImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/k8s/namespaces': {
      id: '/_authenticated/k8s/namespaces'
      path: '/k8s/namespaces'
      fullPath: '/k8s/namespaces'
      preLoaderRoute: typeof AuthenticatedK8sNamespacesImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/k8s/nodes': {
      id: '/_authenticated/k8s/nodes'
      path: '/k8s/nodes'
      fullPath: '/k8s/nodes'
      preLoaderRoute: typeof AuthenticatedK8sNodesImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/k8s/pods': {
      id: '/_authenticated/k8s/pods'
      path: '/k8s/pods'
      fullPath: '/k8s/pods'
      preLoaderRoute: typeof AuthenticatedK8sPodsImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/k8s/secrets': {
      id: '/_authenticated/k8s/secrets'
      path: '/k8s/secrets'
      fullPath: '/k8s/secrets'
      preLoaderRoute: typeof AuthenticatedK8sSecretsImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/k8s/services': {
      id: '/_authenticated/k8s/services'
      path: '/k8s/services'
      fullPath: '/k8s/services'
      preLoaderRoute: typeof AuthenticatedK8sServicesImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/k8s/volumes': {
      id: '/_authenticated/k8s/volumes'
      path: '/k8s/volumes'
      fullPath: '/k8s/volumes'
      preLoaderRoute: typeof AuthenticatedK8sVolumesImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/k8s/volumes_claims': {
      id: '/_authenticated/k8s/volumes_claims'
      path: '/k8s/volumes_claims'
      fullPath: '/k8s/volumes_claims'
      preLoaderRoute: typeof AuthenticatedK8sVolumesclaimsImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/k8sgraph/k8sgraph': {
      id: '/_authenticated/k8sgraph/k8sgraph'
      path: '/k8sgraph/k8sgraph'
      fullPath: '/k8sgraph/k8sgraph'
      preLoaderRoute: typeof AuthenticatedK8sgraphK8sgraphImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/settings/account': {
      id: '/_authenticated/settings/account'
      path: '/account'
      fullPath: '/settings/account'
      preLoaderRoute: typeof AuthenticatedSettingsAccountImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/settings/appearance': {
      id: '/_authenticated/settings/appearance'
      path: '/appearance'
      fullPath: '/settings/appearance'
      preLoaderRoute: typeof AuthenticatedSettingsAppearanceImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/settings/display': {
      id: '/_authenticated/settings/display'
      path: '/display'
      fullPath: '/settings/display'
      preLoaderRoute: typeof AuthenticatedSettingsDisplayImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/settings/notifications': {
      id: '/_authenticated/settings/notifications'
      path: '/notifications'
      fullPath: '/settings/notifications'
      preLoaderRoute: typeof AuthenticatedSettingsNotificationsImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/apps/': {
      id: '/_authenticated/apps/'
      path: '/apps'
      fullPath: '/apps'
      preLoaderRoute: typeof AuthenticatedAppsIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/backup/': {
      id: '/_authenticated/backup/'
      path: '/backup'
      fullPath: '/backup'
      preLoaderRoute: typeof AuthenticatedBackupIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/chats/': {
      id: '/_authenticated/chats/'
      path: '/chats'
      fullPath: '/chats'
      preLoaderRoute: typeof AuthenticatedChatsIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/help-center/': {
      id: '/_authenticated/help-center/'
      path: '/help-center'
      fullPath: '/help-center'
      preLoaderRoute: typeof AuthenticatedHelpCenterIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/k8s/': {
      id: '/_authenticated/k8s/'
      path: '/k8s'
      fullPath: '/k8s'
      preLoaderRoute: typeof AuthenticatedK8sIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/networksecurity/': {
      id: '/_authenticated/networksecurity/'
      path: '/networksecurity'
      fullPath: '/networksecurity'
      preLoaderRoute: typeof AuthenticatedNetworksecurityIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/roles/': {
      id: '/_authenticated/roles/'
      path: '/roles'
      fullPath: '/roles'
      preLoaderRoute: typeof AuthenticatedRolesIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/settings/': {
      id: '/_authenticated/settings/'
      path: '/'
      fullPath: '/settings/'
      preLoaderRoute: typeof AuthenticatedSettingsIndexImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/tasks/': {
      id: '/_authenticated/tasks/'
      path: '/tasks'
      fullPath: '/tasks'
      preLoaderRoute: typeof AuthenticatedTasksIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/users/': {
      id: '/_authenticated/users/'
      path: '/users'
      fullPath: '/users'
      preLoaderRoute: typeof AuthenticatedUsersIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/vulnerability/': {
      id: '/_authenticated/vulnerability/'
      path: '/vulnerability'
      fullPath: '/vulnerability'
      preLoaderRoute: typeof AuthenticatedVulnerabilityIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
  }
}

// Create and export the route tree

interface AuthenticatedSettingsRouteRouteChildren {
  AuthenticatedSettingsAccountRoute: typeof AuthenticatedSettingsAccountRoute
  AuthenticatedSettingsAppearanceRoute: typeof AuthenticatedSettingsAppearanceRoute
  AuthenticatedSettingsDisplayRoute: typeof AuthenticatedSettingsDisplayRoute
  AuthenticatedSettingsNotificationsRoute: typeof AuthenticatedSettingsNotificationsRoute
  AuthenticatedSettingsIndexRoute: typeof AuthenticatedSettingsIndexRoute
}

const AuthenticatedSettingsRouteRouteChildren: AuthenticatedSettingsRouteRouteChildren =
  {
    AuthenticatedSettingsAccountRoute: AuthenticatedSettingsAccountRoute,
    AuthenticatedSettingsAppearanceRoute: AuthenticatedSettingsAppearanceRoute,
    AuthenticatedSettingsDisplayRoute: AuthenticatedSettingsDisplayRoute,
    AuthenticatedSettingsNotificationsRoute:
      AuthenticatedSettingsNotificationsRoute,
    AuthenticatedSettingsIndexRoute: AuthenticatedSettingsIndexRoute,
  }

const AuthenticatedSettingsRouteRouteWithChildren =
  AuthenticatedSettingsRouteRoute._addFileChildren(
    AuthenticatedSettingsRouteRouteChildren,
  )

interface AuthenticatedRouteRouteChildren {
  AuthenticatedSettingsRouteRoute: typeof AuthenticatedSettingsRouteRouteWithChildren
  AuthenticatedIndexRoute: typeof AuthenticatedIndexRoute
  AuthenticatedK8sConfigmapsRoute: typeof AuthenticatedK8sConfigmapsRoute
  AuthenticatedK8sCronjobsRoute: typeof AuthenticatedK8sCronjobsRoute
  AuthenticatedK8sDaemonsetsRoute: typeof AuthenticatedK8sDaemonsetsRoute
  AuthenticatedK8sDeploymentsRoute: typeof AuthenticatedK8sDeploymentsRoute
  AuthenticatedK8sIngressesRoute: typeof AuthenticatedK8sIngressesRoute
  AuthenticatedK8sNamespacesRoute: typeof AuthenticatedK8sNamespacesRoute
  AuthenticatedK8sNodesRoute: typeof AuthenticatedK8sNodesRoute
  AuthenticatedK8sPodsRoute: typeof AuthenticatedK8sPodsRoute
  AuthenticatedK8sSecretsRoute: typeof AuthenticatedK8sSecretsRoute
  AuthenticatedK8sServicesRoute: typeof AuthenticatedK8sServicesRoute
  AuthenticatedK8sVolumesRoute: typeof AuthenticatedK8sVolumesRoute
  AuthenticatedK8sVolumesclaimsRoute: typeof AuthenticatedK8sVolumesclaimsRoute
  AuthenticatedK8sgraphK8sgraphRoute: typeof AuthenticatedK8sgraphK8sgraphRoute
  AuthenticatedAppsIndexRoute: typeof AuthenticatedAppsIndexRoute
  AuthenticatedBackupIndexRoute: typeof AuthenticatedBackupIndexRoute
  AuthenticatedChatsIndexRoute: typeof AuthenticatedChatsIndexRoute
  AuthenticatedHelpCenterIndexRoute: typeof AuthenticatedHelpCenterIndexRoute
  AuthenticatedK8sIndexRoute: typeof AuthenticatedK8sIndexRoute
  AuthenticatedNetworksecurityIndexRoute: typeof AuthenticatedNetworksecurityIndexRoute
  AuthenticatedRolesIndexRoute: typeof AuthenticatedRolesIndexRoute
  AuthenticatedTasksIndexRoute: typeof AuthenticatedTasksIndexRoute
  AuthenticatedUsersIndexRoute: typeof AuthenticatedUsersIndexRoute
  AuthenticatedVulnerabilityIndexRoute: typeof AuthenticatedVulnerabilityIndexRoute
}

const AuthenticatedRouteRouteChildren: AuthenticatedRouteRouteChildren = {
  AuthenticatedSettingsRouteRoute: AuthenticatedSettingsRouteRouteWithChildren,
  AuthenticatedIndexRoute: AuthenticatedIndexRoute,
  AuthenticatedK8sConfigmapsRoute: AuthenticatedK8sConfigmapsRoute,
  AuthenticatedK8sCronjobsRoute: AuthenticatedK8sCronjobsRoute,
  AuthenticatedK8sDaemonsetsRoute: AuthenticatedK8sDaemonsetsRoute,
  AuthenticatedK8sDeploymentsRoute: AuthenticatedK8sDeploymentsRoute,
  AuthenticatedK8sIngressesRoute: AuthenticatedK8sIngressesRoute,
  AuthenticatedK8sNamespacesRoute: AuthenticatedK8sNamespacesRoute,
  AuthenticatedK8sNodesRoute: AuthenticatedK8sNodesRoute,
  AuthenticatedK8sPodsRoute: AuthenticatedK8sPodsRoute,
  AuthenticatedK8sSecretsRoute: AuthenticatedK8sSecretsRoute,
  AuthenticatedK8sServicesRoute: AuthenticatedK8sServicesRoute,
  AuthenticatedK8sVolumesRoute: AuthenticatedK8sVolumesRoute,
  AuthenticatedK8sVolumesclaimsRoute: AuthenticatedK8sVolumesclaimsRoute,
  AuthenticatedK8sgraphK8sgraphRoute: AuthenticatedK8sgraphK8sgraphRoute,
  AuthenticatedAppsIndexRoute: AuthenticatedAppsIndexRoute,
  AuthenticatedBackupIndexRoute: AuthenticatedBackupIndexRoute,
  AuthenticatedChatsIndexRoute: AuthenticatedChatsIndexRoute,
  AuthenticatedHelpCenterIndexRoute: AuthenticatedHelpCenterIndexRoute,
  AuthenticatedK8sIndexRoute: AuthenticatedK8sIndexRoute,
  AuthenticatedNetworksecurityIndexRoute:
    AuthenticatedNetworksecurityIndexRoute,
  AuthenticatedRolesIndexRoute: AuthenticatedRolesIndexRoute,
  AuthenticatedTasksIndexRoute: AuthenticatedTasksIndexRoute,
  AuthenticatedUsersIndexRoute: AuthenticatedUsersIndexRoute,
  AuthenticatedVulnerabilityIndexRoute: AuthenticatedVulnerabilityIndexRoute,
}

const AuthenticatedRouteRouteWithChildren =
  AuthenticatedRouteRoute._addFileChildren(AuthenticatedRouteRouteChildren)

export interface FileRoutesByFullPath {
  '': typeof AuthenticatedRouteRouteWithChildren
  '/settings': typeof AuthenticatedSettingsRouteRouteWithChildren
  '/sign-in': typeof authSignInRoute
  '/sign-in-2': typeof authSignIn2Route
  '/401': typeof errors401Route
  '/403': typeof errors403Route
  '/404': typeof errors404Route
  '/500': typeof errors500Route
  '/503': typeof errors503Route
  '/': typeof AuthenticatedIndexRoute
  '/k8s/configmaps': typeof AuthenticatedK8sConfigmapsRoute
  '/k8s/cronjobs': typeof AuthenticatedK8sCronjobsRoute
  '/k8s/daemonsets': typeof AuthenticatedK8sDaemonsetsRoute
  '/k8s/deployments': typeof AuthenticatedK8sDeploymentsRoute
  '/k8s/ingresses': typeof AuthenticatedK8sIngressesRoute
  '/k8s/namespaces': typeof AuthenticatedK8sNamespacesRoute
  '/k8s/nodes': typeof AuthenticatedK8sNodesRoute
  '/k8s/pods': typeof AuthenticatedK8sPodsRoute
  '/k8s/secrets': typeof AuthenticatedK8sSecretsRoute
  '/k8s/services': typeof AuthenticatedK8sServicesRoute
  '/k8s/volumes': typeof AuthenticatedK8sVolumesRoute
  '/k8s/volumes_claims': typeof AuthenticatedK8sVolumesclaimsRoute
  '/k8sgraph/k8sgraph': typeof AuthenticatedK8sgraphK8sgraphRoute
  '/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/apps': typeof AuthenticatedAppsIndexRoute
  '/backup': typeof AuthenticatedBackupIndexRoute
  '/chats': typeof AuthenticatedChatsIndexRoute
  '/help-center': typeof AuthenticatedHelpCenterIndexRoute
  '/k8s': typeof AuthenticatedK8sIndexRoute
  '/networksecurity': typeof AuthenticatedNetworksecurityIndexRoute
  '/roles': typeof AuthenticatedRolesIndexRoute
  '/settings/': typeof AuthenticatedSettingsIndexRoute
  '/tasks': typeof AuthenticatedTasksIndexRoute
  '/users': typeof AuthenticatedUsersIndexRoute
  '/vulnerability': typeof AuthenticatedVulnerabilityIndexRoute
}

export interface FileRoutesByTo {
  '/sign-in': typeof authSignInRoute
  '/sign-in-2': typeof authSignIn2Route
  '/401': typeof errors401Route
  '/403': typeof errors403Route
  '/404': typeof errors404Route
  '/500': typeof errors500Route
  '/503': typeof errors503Route
  '/': typeof AuthenticatedIndexRoute
  '/k8s/configmaps': typeof AuthenticatedK8sConfigmapsRoute
  '/k8s/cronjobs': typeof AuthenticatedK8sCronjobsRoute
  '/k8s/daemonsets': typeof AuthenticatedK8sDaemonsetsRoute
  '/k8s/deployments': typeof AuthenticatedK8sDeploymentsRoute
  '/k8s/ingresses': typeof AuthenticatedK8sIngressesRoute
  '/k8s/namespaces': typeof AuthenticatedK8sNamespacesRoute
  '/k8s/nodes': typeof AuthenticatedK8sNodesRoute
  '/k8s/pods': typeof AuthenticatedK8sPodsRoute
  '/k8s/secrets': typeof AuthenticatedK8sSecretsRoute
  '/k8s/services': typeof AuthenticatedK8sServicesRoute
  '/k8s/volumes': typeof AuthenticatedK8sVolumesRoute
  '/k8s/volumes_claims': typeof AuthenticatedK8sVolumesclaimsRoute
  '/k8sgraph/k8sgraph': typeof AuthenticatedK8sgraphK8sgraphRoute
  '/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/apps': typeof AuthenticatedAppsIndexRoute
  '/backup': typeof AuthenticatedBackupIndexRoute
  '/chats': typeof AuthenticatedChatsIndexRoute
  '/help-center': typeof AuthenticatedHelpCenterIndexRoute
  '/k8s': typeof AuthenticatedK8sIndexRoute
  '/networksecurity': typeof AuthenticatedNetworksecurityIndexRoute
  '/roles': typeof AuthenticatedRolesIndexRoute
  '/settings': typeof AuthenticatedSettingsIndexRoute
  '/tasks': typeof AuthenticatedTasksIndexRoute
  '/users': typeof AuthenticatedUsersIndexRoute
  '/vulnerability': typeof AuthenticatedVulnerabilityIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/_authenticated': typeof AuthenticatedRouteRouteWithChildren
  '/_authenticated/settings': typeof AuthenticatedSettingsRouteRouteWithChildren
  '/(auth)/sign-in': typeof authSignInRoute
  '/(auth)/sign-in-2': typeof authSignIn2Route
  '/(errors)/401': typeof errors401Route
  '/(errors)/403': typeof errors403Route
  '/(errors)/404': typeof errors404Route
  '/(errors)/500': typeof errors500Route
  '/(errors)/503': typeof errors503Route
  '/_authenticated/': typeof AuthenticatedIndexRoute
  '/_authenticated/k8s/configmaps': typeof AuthenticatedK8sConfigmapsRoute
  '/_authenticated/k8s/cronjobs': typeof AuthenticatedK8sCronjobsRoute
  '/_authenticated/k8s/daemonsets': typeof AuthenticatedK8sDaemonsetsRoute
  '/_authenticated/k8s/deployments': typeof AuthenticatedK8sDeploymentsRoute
  '/_authenticated/k8s/ingresses': typeof AuthenticatedK8sIngressesRoute
  '/_authenticated/k8s/namespaces': typeof AuthenticatedK8sNamespacesRoute
  '/_authenticated/k8s/nodes': typeof AuthenticatedK8sNodesRoute
  '/_authenticated/k8s/pods': typeof AuthenticatedK8sPodsRoute
  '/_authenticated/k8s/secrets': typeof AuthenticatedK8sSecretsRoute
  '/_authenticated/k8s/services': typeof AuthenticatedK8sServicesRoute
  '/_authenticated/k8s/volumes': typeof AuthenticatedK8sVolumesRoute
  '/_authenticated/k8s/volumes_claims': typeof AuthenticatedK8sVolumesclaimsRoute
  '/_authenticated/k8sgraph/k8sgraph': typeof AuthenticatedK8sgraphK8sgraphRoute
  '/_authenticated/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/_authenticated/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/_authenticated/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/_authenticated/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/_authenticated/apps/': typeof AuthenticatedAppsIndexRoute
  '/_authenticated/backup/': typeof AuthenticatedBackupIndexRoute
  '/_authenticated/chats/': typeof AuthenticatedChatsIndexRoute
  '/_authenticated/help-center/': typeof AuthenticatedHelpCenterIndexRoute
  '/_authenticated/k8s/': typeof AuthenticatedK8sIndexRoute
  '/_authenticated/networksecurity/': typeof AuthenticatedNetworksecurityIndexRoute
  '/_authenticated/roles/': typeof AuthenticatedRolesIndexRoute
  '/_authenticated/settings/': typeof AuthenticatedSettingsIndexRoute
  '/_authenticated/tasks/': typeof AuthenticatedTasksIndexRoute
  '/_authenticated/users/': typeof AuthenticatedUsersIndexRoute
  '/_authenticated/vulnerability/': typeof AuthenticatedVulnerabilityIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | ''
    | '/settings'
    | '/sign-in'
    | '/sign-in-2'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/'
    | '/k8s/configmaps'
    | '/k8s/cronjobs'
    | '/k8s/daemonsets'
    | '/k8s/deployments'
    | '/k8s/ingresses'
    | '/k8s/namespaces'
    | '/k8s/nodes'
    | '/k8s/pods'
    | '/k8s/secrets'
    | '/k8s/services'
    | '/k8s/volumes'
    | '/k8s/volumes_claims'
    | '/k8sgraph/k8sgraph'
    | '/settings/account'
    | '/settings/appearance'
    | '/settings/display'
    | '/settings/notifications'
    | '/apps'
    | '/backup'
    | '/chats'
    | '/help-center'
    | '/k8s'
    | '/networksecurity'
    | '/roles'
    | '/settings/'
    | '/tasks'
    | '/users'
    | '/vulnerability'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/sign-in'
    | '/sign-in-2'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/'
    | '/k8s/configmaps'
    | '/k8s/cronjobs'
    | '/k8s/daemonsets'
    | '/k8s/deployments'
    | '/k8s/ingresses'
    | '/k8s/namespaces'
    | '/k8s/nodes'
    | '/k8s/pods'
    | '/k8s/secrets'
    | '/k8s/services'
    | '/k8s/volumes'
    | '/k8s/volumes_claims'
    | '/k8sgraph/k8sgraph'
    | '/settings/account'
    | '/settings/appearance'
    | '/settings/display'
    | '/settings/notifications'
    | '/apps'
    | '/backup'
    | '/chats'
    | '/help-center'
    | '/k8s'
    | '/networksecurity'
    | '/roles'
    | '/settings'
    | '/tasks'
    | '/users'
    | '/vulnerability'
  id:
    | '__root__'
    | '/_authenticated'
    | '/_authenticated/settings'
    | '/(auth)/sign-in'
    | '/(auth)/sign-in-2'
    | '/(errors)/401'
    | '/(errors)/403'
    | '/(errors)/404'
    | '/(errors)/500'
    | '/(errors)/503'
    | '/_authenticated/'
    | '/_authenticated/k8s/configmaps'
    | '/_authenticated/k8s/cronjobs'
    | '/_authenticated/k8s/daemonsets'
    | '/_authenticated/k8s/deployments'
    | '/_authenticated/k8s/ingresses'
    | '/_authenticated/k8s/namespaces'
    | '/_authenticated/k8s/nodes'
    | '/_authenticated/k8s/pods'
    | '/_authenticated/k8s/secrets'
    | '/_authenticated/k8s/services'
    | '/_authenticated/k8s/volumes'
    | '/_authenticated/k8s/volumes_claims'
    | '/_authenticated/k8sgraph/k8sgraph'
    | '/_authenticated/settings/account'
    | '/_authenticated/settings/appearance'
    | '/_authenticated/settings/display'
    | '/_authenticated/settings/notifications'
    | '/_authenticated/apps/'
    | '/_authenticated/backup/'
    | '/_authenticated/chats/'
    | '/_authenticated/help-center/'
    | '/_authenticated/k8s/'
    | '/_authenticated/networksecurity/'
    | '/_authenticated/roles/'
    | '/_authenticated/settings/'
    | '/_authenticated/tasks/'
    | '/_authenticated/users/'
    | '/_authenticated/vulnerability/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  AuthenticatedRouteRoute: typeof AuthenticatedRouteRouteWithChildren
  authSignInRoute: typeof authSignInRoute
  authSignIn2Route: typeof authSignIn2Route
  errors401Route: typeof errors401Route
  errors403Route: typeof errors403Route
  errors404Route: typeof errors404Route
  errors500Route: typeof errors500Route
  errors503Route: typeof errors503Route
}

const rootRouteChildren: RootRouteChildren = {
  AuthenticatedRouteRoute: AuthenticatedRouteRouteWithChildren,
  authSignInRoute: authSignInRoute,
  authSignIn2Route: authSignIn2Route,
  errors401Route: errors401Route,
  errors403Route: errors403Route,
  errors404Route: errors404Route,
  errors500Route: errors500Route,
  errors503Route: errors503Route,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/_authenticated",
        "/(auth)/sign-in",
        "/(auth)/sign-in-2",
        "/(errors)/401",
        "/(errors)/403",
        "/(errors)/404",
        "/(errors)/500",
        "/(errors)/503"
      ]
    },
    "/_authenticated": {
      "filePath": "_authenticated/route.tsx",
      "children": [
        "/_authenticated/settings",
        "/_authenticated/",
        "/_authenticated/k8s/configmaps",
        "/_authenticated/k8s/cronjobs",
        "/_authenticated/k8s/daemonsets",
        "/_authenticated/k8s/deployments",
        "/_authenticated/k8s/ingresses",
        "/_authenticated/k8s/namespaces",
        "/_authenticated/k8s/nodes",
        "/_authenticated/k8s/pods",
        "/_authenticated/k8s/secrets",
        "/_authenticated/k8s/services",
        "/_authenticated/k8s/volumes",
        "/_authenticated/k8s/volumes_claims",
        "/_authenticated/k8sgraph/k8sgraph",
        "/_authenticated/apps/",
        "/_authenticated/backup/",
        "/_authenticated/chats/",
        "/_authenticated/help-center/",
        "/_authenticated/k8s/",
        "/_authenticated/networksecurity/",
        "/_authenticated/roles/",
        "/_authenticated/tasks/",
        "/_authenticated/users/",
        "/_authenticated/vulnerability/"
      ]
    },
    "/_authenticated/settings": {
      "filePath": "_authenticated/settings/route.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/settings/account",
        "/_authenticated/settings/appearance",
        "/_authenticated/settings/display",
        "/_authenticated/settings/notifications",
        "/_authenticated/settings/"
      ]
    },
    "/(auth)/sign-in": {
      "filePath": "(auth)/sign-in.tsx"
    },
    "/(auth)/sign-in-2": {
      "filePath": "(auth)/sign-in-2.tsx"
    },
    "/(errors)/401": {
      "filePath": "(errors)/401.tsx"
    },
    "/(errors)/403": {
      "filePath": "(errors)/403.tsx"
    },
    "/(errors)/404": {
      "filePath": "(errors)/404.tsx"
    },
    "/(errors)/500": {
      "filePath": "(errors)/500.tsx"
    },
    "/(errors)/503": {
      "filePath": "(errors)/503.tsx"
    },
    "/_authenticated/": {
      "filePath": "_authenticated/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/k8s/configmaps": {
      "filePath": "_authenticated/k8s/configmaps.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/k8s/cronjobs": {
      "filePath": "_authenticated/k8s/cronjobs.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/k8s/daemonsets": {
      "filePath": "_authenticated/k8s/daemonsets.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/k8s/deployments": {
      "filePath": "_authenticated/k8s/deployments.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/k8s/ingresses": {
      "filePath": "_authenticated/k8s/ingresses.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/k8s/namespaces": {
      "filePath": "_authenticated/k8s/namespaces.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/k8s/nodes": {
      "filePath": "_authenticated/k8s/nodes.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/k8s/pods": {
      "filePath": "_authenticated/k8s/pods.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/k8s/secrets": {
      "filePath": "_authenticated/k8s/secrets.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/k8s/services": {
      "filePath": "_authenticated/k8s/services.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/k8s/volumes": {
      "filePath": "_authenticated/k8s/volumes.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/k8s/volumes_claims": {
      "filePath": "_authenticated/k8s/volumes_claims.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/k8sgraph/k8sgraph": {
      "filePath": "_authenticated/k8sgraph/k8sgraph.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/settings/account": {
      "filePath": "_authenticated/settings/account.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/settings/appearance": {
      "filePath": "_authenticated/settings/appearance.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/settings/display": {
      "filePath": "_authenticated/settings/display.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/settings/notifications": {
      "filePath": "_authenticated/settings/notifications.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/apps/": {
      "filePath": "_authenticated/apps/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/backup/": {
      "filePath": "_authenticated/backup/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/chats/": {
      "filePath": "_authenticated/chats/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/help-center/": {
      "filePath": "_authenticated/help-center/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/k8s/": {
      "filePath": "_authenticated/k8s/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/networksecurity/": {
      "filePath": "_authenticated/networksecurity/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/roles/": {
      "filePath": "_authenticated/roles/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/settings/": {
      "filePath": "_authenticated/settings/index.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/tasks/": {
      "filePath": "_authenticated/tasks/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/users/": {
      "filePath": "_authenticated/users/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/vulnerability/": {
      "filePath": "_authenticated/vulnerability/index.tsx",
      "parent": "/_authenticated"
    }
  }
}
ROUTE_MANIFEST_END */
