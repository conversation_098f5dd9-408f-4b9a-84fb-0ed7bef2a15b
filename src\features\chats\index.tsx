import React, { useState, useRef, useEffect } from "react";
import { format } from "date-fns";
import { IconMessages, IconSend } from "@tabler/icons-react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Header } from "@/components/layout/header";
import { Main } from "@/components/layout/main";
import { ProfileDropdown } from "@/components/profile-dropdown";
import { Search } from "@/components/search";
import { ThemeSwitch } from "@/components/theme-switch";
import { Light as SyntaxHighlighter } from "react-syntax-highlighter";
import js from "react-syntax-highlighter/dist/esm/languages/hljs/javascript";
import bash from "react-syntax-highlighter/dist/esm/languages/hljs/bash";
import yaml from "react-syntax-highlighter/dist/esm/languages/hljs/yaml";
import go from "react-syntax-highlighter/dist/esm/languages/hljs/go";
import java from "react-syntax-highlighter/dist/esm/languages/hljs/java";
import { dracula } from "react-syntax-highlighter/dist/esm/styles/hljs";
import { useDebounce } from "use-debounce";

SyntaxHighlighter.registerLanguage("javascript", js);
SyntaxHighlighter.registerLanguage("bash", bash);
SyntaxHighlighter.registerLanguage("yaml", yaml);
SyntaxHighlighter.registerLanguage("go", go);
SyntaxHighlighter.registerLanguage("java", java);

interface Message {
  role: "user" | "assistant";
  content: string;
  timestamp: Date;
}

export default function Chats() {
  const [prompt, setPrompt] = useState("");
  const [debouncedPrompt] = useDebounce(prompt, 500);
  const [streamedResponse, setStreamedResponse] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [chatHistory, setChatHistory] = useState<Message[]>([]);
  const abortControllerRef = useRef<AbortController | null>(null);
  const bottomRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    bottomRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [chatHistory, streamedResponse]);

  const handleSubmit = async (e?: React.FormEvent) => {
    e?.preventDefault();
    if (!debouncedPrompt.trim()) return;

    const userMsg: Message = {
      role: "user",
      content: debouncedPrompt,
      timestamp: new Date(),
    };
    setChatHistory((prev) => [...prev, userMsg]);
    setStreamedResponse("");
    setIsLoading(true);
    setPrompt(""); // 🔹 input temizleniyor

    const controller = new AbortController();
    abortControllerRef.current = controller;
    const signal = controller.signal;

    try {
      const response = await fetch("/offline-ai/api/generate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          model: "hf.co/dereklck/kubernetes_operator_3b_peft_gguf:latest",
          prompt: debouncedPrompt,
        }),
        signal,
      });

      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
      const reader = response.body?.getReader();
      if (!reader) throw new Error("No response body");

      const decoder = new TextDecoder();
      let partial = "", full = "";

      while (true) {
        if (signal.aborted) break;
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = (partial + chunk).split("\n");
        partial = lines.pop() || "";

        for (const line of lines) {
          if (!line.trim()) continue;
          try {
            const j = JSON.parse(line);
            if (j.response) {
              full += j.response;
              setStreamedResponse((p) => p + j.response);
            }
          } catch { }
        }
      }

      setIsLoading(false);
      setChatHistory((prev) => [
        ...prev,
        { role: "assistant", content: full, timestamp: new Date() },
      ]);
    } catch (err: any) {
      if (err.name === "AbortError") {
        setChatHistory((prev) => {
          const last = prev[prev.length - 1];
          if (last.role === "user") {
            return [...prev.slice(0, -1), { ...last, content: last.content + "\n[Stopped]" }];
          }
          return prev;
        });
      } else {
        setStreamedResponse("Error occurred...");
      }
      setIsLoading(false);
    }
  };

  const handleStop = () => {
    setIsLoading(false);
    abortControllerRef.current?.abort();
  };

  const renderMessage = (content: string) => {
    const re = /```(bash|javascript|yaml|go|java)?\n([\s\S]*?)\n```/g;
    const parts: React.ReactNode[] = [];
    let m, last = 0;

    while ((m = re.exec(content))) {
      if (m.index > last) parts.push(content.slice(last, m.index));
      parts.push(
        <SyntaxHighlighter language={m[1] || "bash"} style={dracula} key={m.index}>
          {m[2]}
        </SyntaxHighlighter>
      );
      last = re.lastIndex;
    }
    if (last < content.length) parts.push(content.slice(last));
    return parts;
  };

  return (
    <>
      <Header>
        <Search />
        <div className="ml-auto flex items-center space-x-4">
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>
      <Main fixed>
        <section className="flex h-full gap-6 w-full">
          <div className="flex flex-col w-full h-full bg-muted border rounded-lg">
            {/* Header */}
            <div className="flex items-center gap-4 p-4 border-b">
              <Avatar className="size-9"><AvatarFallback>AI</AvatarFallback></Avatar>
              <div>
                <div className="font-semibold text-sm">AI Assistant</div>
                <div className="text-xs text-muted-foreground">Kubernetes Operator</div>
              </div>
            </div>

            {/* Chat scrollable area */}
            <div className="flex-1 overflow-auto flex flex-col px-4 pb-2 gap-2">
              {chatHistory.length === 0 && (
                <div className="text-center text-muted-foreground mt-10">
                  <IconMessages size={32} className="mx-auto mb-2" />
                  <p>Ask me anything about Kubernetes!</p>
                </div>
              )}
              {chatHistory.map((msg, i) => (
                <div key={i} className={`flex ${msg.role === "user" ? "justify-end" : "justify-start"}`}>
                  {msg.role === "assistant" && <Avatar className="mr-2"><AvatarFallback>AI</AvatarFallback></Avatar>}
                  <div className="bg-background border rounded p-2 max-w-lg whitespace-pre-wrap">
                    {renderMessage(msg.content)}
                    <div className="text-xs text-muted-foreground text-right">
                      {format(msg.timestamp, "h:mm a")}
                    </div>
                  </div>
                  {msg.role === "user" && <Avatar className="ml-2"><AvatarFallback>You</AvatarFallback></Avatar>}
                </div>
              ))}
              {isLoading && (
                <div className="flex justify-start">
                  <Avatar className="mr-2"><AvatarFallback>AI</AvatarFallback></Avatar>
                  <div className="bg-background border rounded p-2 max-w-lg whitespace-pre-wrap">
                    {renderMessage(streamedResponse)}
                  </div>
                </div>
              )}
              <div ref={bottomRef} />
            </div>


            {/* Fixed footer input */}
            <form className="sticky bottom-0 bg-white border-t p-4 flex gap-2" onSubmit={handleSubmit}>
              <input
                ref={inputRef}
                type="text"
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="Ask about Kubernetes..."
                disabled={isLoading}
                className="flex-1 border px-3 py-2 rounded"
              />
              {isLoading ? (
                <Button variant="destructive" onClick={handleStop}>
                  Stop
                </Button>
              ) : (
                <Button type="submit" disabled={!prompt.trim()}>
                  <IconSend size={18} />
                </Button>
              )}
            </form>
          </div>
        </section>
      </Main>
    </>
  );
}
