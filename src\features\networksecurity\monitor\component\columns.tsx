import { ColumnDef } from "@tanstack/react-table"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { DataTableRowActions } from "./data-table-row-actions"
import { DataTableColumnHeader } from "../../component/data-table-column-header"

export type Packet = {
  id: string;
  sessionId: string;
  sourcePodName: string;
  sourcePodNamespace: string;
  sourcePodLabel: string;
  destinationPodName: string;
  destinationPodNamespace: string;
  destinationPodLabel: string;
  interfaceName: string;
  sourceIp: string;
  sourcePort: number;
  destinationIp: string;
  destinationPort: number;
  startTime: string;
  endTime: string;
  protocol: string;
  responseExists: string;
}

export const columns: ColumnDef<Packet>[] = [
   {
    id: 'select',
    header: () => null, // Remove header checkbox for multiple selection
    cell: ({ row, table }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => {
          // Deselect all rows first
          table.toggleAllRowsSelected(false)
          // Then select only this row
          row.toggleSelected(!!value)
        }}
        aria-label='Select row'
        className='translate-y-[2px]'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "startTime",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Start Time" />
    ),
    cell: ({ row }) => {
      return <div>{row.getValue("startTime")}</div>
    },
  },
  {
    accessorKey: "endTime",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="End Time" />
    ),
    cell: ({ row }) => {
      return <div>{row.getValue("endTime")}</div>
    },
  },
  {
    accessorKey: "sourcePodName",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Source Pod" />
    ),
    cell: ({ row }) => {
      return <div className="font-medium">{row.getValue("sourcePodName")}</div>
    },
  },
  {
    accessorKey: "sourcePodNamespace",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Source Namespace" />
    ),
    cell: ({ row }) => {
      return <div>{row.getValue("sourcePodNamespace")}</div>
    },
  },
  {
    accessorKey: "sourceIp",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Source IP" />
    ),
    cell: ({ row }) => {
      return <div>{row.getValue("sourceIp")}</div>
    },
  },
  {
    accessorKey: "sourcePort",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Source Port" />
    ),
    cell: ({ row }) => {
      return <div>{row.getValue("sourcePort")}</div>
    },
  },
  {
    accessorKey: "destinationPodName",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Destination Pod" />
    ),
    cell: ({ row }) => {
      return <div className="font-medium">{row.getValue("destinationPodName")}</div>
    },
  },
  {
    accessorKey: "destinationPodNamespace",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Destination Namespace" />
    ),
    cell: ({ row }) => {
      return <div>{row.getValue("destinationPodNamespace")}</div>
    },
  },
  {
    accessorKey: "destinationIp",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Destination IP" />
    ),
    cell: ({ row }) => {
      return <div>{row.getValue("destinationIp")}</div>
    },
  },
  {
    accessorKey: "destinationPort",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Destination Port" />
    ),
    cell: ({ row }) => {
      return <div>{row.getValue("destinationPort")}</div>
    },
  },
  {
    accessorKey: "protocol",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Protocol" />
    ),
    cell: ({ row }) => {
      return <div>{row.getValue("protocol")}</div>
    },
  },
  {
    accessorKey: "responseExists",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Response Exists" />
    ),
    cell: ({ row }) => {
      const value = row.getValue("responseExists");
      return (
        <Badge variant={value === "true" ? "default" : "destructive"}>
          {value === "true" ? "Yes" : "No"}
        </Badge>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
];




