import { DotsHorizontalIcon } from '@radix-ui/react-icons'
import { Row } from '@tanstack/react-table'
import { IconTrash, IconUserShield } from '@tabler/icons-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useUsers } from '../context/users-context'
import { User } from '../data/schema'

interface DataTableRowActionsProps {
  row: Row<User>
}

export function DataTableRowActions({ row }: DataTableRowActionsProps) {
  // Wrap the hook usage in a try/catch to provide a more helpful error message
  let userContext;
  try {
    userContext = useUsers();
  } catch (error) {
    console.error("DataTableRowActions must be used within a UsersProvider");
    // Return a simplified version that doesn't use context
    return (
      <Button
        variant='ghost'
        className='flex h-8 w-8 p-0 data-[state=open]:bg-muted'
        disabled
      >
        <DotsHorizontalIcon className='h-4 w-4' />
        <span className='sr-only'>Menu unavailable</span>
      </Button>
    );
  }
  
  const { setOpen, setCurrentRow } = userContext;
  const user = row.original;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          className='flex h-8 w-8 p-0 data-[state=open]:bg-muted'
        >
          <DotsHorizontalIcon className='h-4 w-4' />
          <span className='sr-only'>Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='w-[160px]'>
        <DropdownMenuItem
          onClick={() => {
            setCurrentRow(user)
            setOpen('roles')
          }}
        >
          <IconUserShield className='mr-2 h-3.5 w-3.5 text-muted-foreground/70' />
          Assign Roles
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() => {
            setCurrentRow(user)
            setOpen('delete')
          }}
        >
          <IconTrash className='mr-2 h-3.5 w-3.5 text-muted-foreground/70' />
          Delete
          <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
