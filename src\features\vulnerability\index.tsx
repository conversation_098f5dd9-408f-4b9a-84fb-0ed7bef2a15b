import { useState, useEffect } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import axios_api from '@/lib/axios_api';
import { Header } from '@/components/layout/header';
import { Main } from '@/components/layout/main';
import { ThemeSwitch } from '@/components/theme-switch';
import { ProfileDropdown } from '@/components/profile-dropdown';
import { Search } from '@/components/search';

import { DataTable } from './components/data-table';
import { columns } from './components/columns';
import ScanReport from './components/scan-report';


interface VulnResult {
  id: number;
  created: string;
  image: string;
  object: string;
  last: string;
  resultJson: string;
  low: number;
  high: number;
  crict: number;
  medium: number;
}


export default function Vulnerability() {
  const [vulnResults, setVulnResults] = useState<VulnResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedVulnResult, setSelectedVulnResult] = useState<VulnResult | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const resultsResponse = await axios_api.get('/vulnresults');
        const resultsData: VulnResult[] = resultsResponse.data;
        setVulnResults(resultsData);
      } catch (err) {
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);



  const handleDetailsClick = (vulnResult: VulnResult) => {
    setSelectedVulnResult(vulnResult);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedVulnResult(null);
  };

  return (
    <>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        {/* Vulnerability Table */}
        <Card>
          <CardContent>
            <DataTable
              columns={columns(handleDetailsClick)}
              data={vulnResults}
              loading={loading}
              title="Vulnerability Dashboard"
              description="Overview of vulnerabilities detected in your container images"
            />
          </CardContent>
        </Card>
        {/* ScanReport Modal */}
        <Dialog
          open={isModalOpen}
          onOpenChange={handleCloseModal}
        >
          <DialogContent className="fixed min-h-[80vh] min-w-[80vw] p-6 overflow-y-auto">
            <div className="container h-[80vh] w-[80vw] p-6">
              {selectedVulnResult && (
                <ScanReport
                  image={selectedVulnResult.image}
                  id={selectedVulnResult.id}
                />
              )}
            </div>
          </DialogContent>
        </Dialog>

      </Main>


    </>
  );
}