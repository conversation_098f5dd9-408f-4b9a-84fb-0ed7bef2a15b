import { Checkbox } from '@/components/ui/checkbox'
import { ColumnDef } from '@tanstack/react-table'

import { DataTableRowActions } from './data-table-row-actions'
import { DataTableColumnHeader } from '../../components/data-table-column-header'
import { formatDateToLocal } from '@/lib/dateformat';



interface DataEntity {
  id: string;
  name: string;
  namespace: string;
  image: string;
  applicationType: string;
  status: string;
  published: string;
  createdAt: string;
  labels: Record<string, string>;
  annotation: Record<string, string>;
  lastSeenAt: string;
  desiredReplicas: number;
  currentReplicas: number;

}


export const columns: ColumnDef<DataEntity>[] = [
  {
    id: 'select',
    header: () => null, // Remove header checkbox for multiple selection
    cell: ({ row, table }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => {
          // Deselect all rows first
          table.toggleAllRowsSelected(false)
          // Then select only this row
          row.toggleSelected(!!value)
        }}
        aria-label='Select row'
        className='translate-y-[2px]'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Name' />
    ),
    cell: ({ row }) => <div>{row.getValue('name')}</div>,
    enableSorting: true,
    enableHiding: false,
  },{
    accessorKey: 'namespace',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Namespace' />
    ),
    cell: ({ row }) => <div>{row.getValue('namespace')}</div>,
    enableSorting: true,
    enableHiding: false,
  },{
    accessorKey: 'applicationType',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Type' />
    ),
    cell: ({ row }) => <div>{row.getValue('applicationType')}</div>,
    enableSorting: true,
    enableHiding: false,
  },{
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Status' />
    ),
    cell: ({ row }) => <div>{row.getValue('status')}</div>,
    enableSorting: true,
    enableHiding: true,
  },{
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='CreatedAt' />
    ),
    cell: ({ row }) => <div >{formatDateToLocal(row.getValue('createdAt'))}</div>,
    enableSorting: true,
    enableHiding: false,
  },{
    accessorKey: 'published',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Published' />
    ),
    cell: ({ row }) => <div>{row.getValue('published')}</div>,
    enableSorting: true,
    enableHiding: false,
  },{
    accessorKey: 'published',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Published' />
    ),
    cell: ({ row }) => <div>{row.getValue('published')}</div>,
    enableSorting: true,
    enableHiding: false,
  },{
    accessorKey: 'currentReplicas',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Current' />
    ),
    cell: ({ row }) => <div>{row.getValue('currentReplicas')}</div>,
    enableSorting: true,
    enableHiding: true,
  },{
    accessorKey: 'desiredReplicas',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Desired' />
    ),
    cell: ({ row }) => <div>{row.getValue('desiredReplicas')}</div>,
    enableSorting: true,
    enableHiding: true,
  },
  {
    id: 'actions',
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
]



