
import { DotsHorizontalIcon } from '@radix-ui/react-icons'
import { IconEye, IconLoader2 } from '@tabler/icons-react'
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import axios_api from '@/lib/axios_api'
import { toast } from 'sonner'

interface SessionPacket {
  id: string;
  startTime: string;
  sourceIp: string;
  destinationIp: string;
  protocol: string;
  length: number;
  info: string;
  // Add other fields as needed
}

async function getSessionPackets(sessionId: string): Promise<SessionPacket[]> {
  try {
    const url = `/k8s/search_network_packets?sessionId=${sessionId}`; // Adjust your API endpoint
    const response = await axios_api.get(url);

    const data: SessionPacket[] = response.data;
    return data;
  } catch (error) {
    console.error("Error fetching session packets:", error);
    return []; // Or handle the error appropriately
  }
}

interface RowOriginal {
  name?: string;
  namespace?: string;
  labels?: Record<string, string>;
  annotation?: Record<string, string>;
  sessionId?: string;
}

interface DataTableRowActionsProps<TData extends { original: RowOriginal }> {
  row: TData;
}

export function DataTableRowActions<TData extends { original: RowOriginal }>({
  row,
}: DataTableRowActionsProps<TData>) {
  const [showSessionModal, setShowSessionModal] = useState(false)
  const [sessionPackets, setSessionPackets] = useState<SessionPacket[]>([])
  const [loading, setLoading] = useState(false)

  const handleShowSession = async () => {
    if (!row.original.sessionId) {
      toast.error("No session ID available for this record")
      return
    }
    
    setShowSessionModal(true)
    setLoading(true)
    
    try {
      const packets = await getSessionPackets(row.original.sessionId)
      setSessionPackets(packets)
    } catch (error) {
      toast.error("Failed to load session packets")
      console.error(error)
    } finally {
      setLoading(false)
    }
  }

  // Function to get protocol color
  const getProtocolColor = (protocol: string) => {
    switch (protocol.toUpperCase()) {
      case 'TCP':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'UDP':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'ICMP':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'HTTP':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      case 'HTTPS':
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
    }
  }

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleString();
    } catch (e) {
      return timestamp;
    }
  }

  return (
    <>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button
            variant='ghost'
            className='data-[state=open]:bg-muted flex h-8 w-8 p-0'
          >
            <DotsHorizontalIcon className='h-4 w-4' />
            <span className='sr-only'>Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end' className='w-[160px]'>
          <DropdownMenuItem onClick={handleShowSession}>
            Show Session
            <DropdownMenuShortcut>
              <IconEye size={16} />
            </DropdownMenuShortcut>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={showSessionModal} onOpenChange={setShowSessionModal}>
        <DialogContent className="max-w-[80vw] w-[1200px]">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold flex items-center gap-2">
              <IconEye className="h-5 w-5" />
              Session Packets
              {row.original.sessionId && (
                <Badge variant="outline" className="ml-2 text-xs">
                  ID: {row.original.sessionId.substring(0, 8)}...
                </Badge>
              )}
            </DialogTitle>
          </DialogHeader>
          
          {loading ? (
            <div className="flex flex-col items-center justify-center p-12 space-y-4">
              <IconLoader2 className="h-10 w-10 animate-spin text-primary" />
              <p className="text-muted-foreground">Loading session data...</p>
            </div>
          ) : sessionPackets.length > 0 ? (
            <div className="overflow-auto max-h-[60vh] rounded-md border ">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-muted sticky top-0">
                    <th className="p-3 text-left font-medium text-muted-foreground">Time</th>
                    <th className="p-3 text-left font-medium text-muted-foreground">Source</th>
                    <th className="p-3 text-left font-medium text-muted-foreground">Destination</th>
                    <th className="p-3 text-left font-medium text-muted-foreground">Protocol</th>
                    <th className="p-3 text-left font-medium text-muted-foreground">Length</th>
                    <th className="p-3 text-left font-medium text-muted-foreground">Info</th>
                  </tr>
                </thead>
                <tbody>
                  {sessionPackets.map((packet, index) => (
                    <tr key={index} className="border-t hover:bg-muted/50 transition-colors">
                      <td className="p-3 text-sm">{formatTimestamp(packet.startTime)}</td>
                      <td className="p-3 text-sm font-medium">{packet.sourceIp}</td>
                      <td className="p-3 text-sm font-medium">{packet.destinationIp}</td>
                      <td className="p-3">
                        <Badge className={`${getProtocolColor(packet.protocol)}`}>
                          {packet.protocol}
                        </Badge>
                      </td>
                      <td className="p-3 text-sm text-right">{packet.length} bytes</td>
                      <td className="p-3 text-sm truncate max-w-[200px]">
                        <div className="truncate" title={packet.info}>
                          {packet.info}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <Card className="flex flex-col items-center justify-center p-12 bg-muted/20">
              <p className="text-muted-foreground text-center">No packets found for this session</p>
              <Button 
                variant="outline" 
                className="mt-4"
                onClick={() => setShowSessionModal(false)}
              >
                Close
              </Button>
            </Card>
          )}
          
          {sessionPackets.length > 0 && (
            <div className="flex justify-between items-center mt-4 text-sm text-muted-foreground">
              <div>Total packets: {sessionPackets.length}</div>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setShowSessionModal(false)}
              >
                Close
              </Button>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}
