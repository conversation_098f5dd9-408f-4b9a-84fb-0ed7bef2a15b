import React, { useState, useEffect, useCallback } from 'react';
import {
  ReactFlow,
  Background,
  Controls,
  BaseEdge,
  getSmoothStepPath,
  useNodesState,
  useEdgesState,
  Panel,
  Node,
  Edge,
  Position
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { Rocket, Server, Globe, Database } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import axios_api from '@/lib/axios_api';
import NetworkYamlEditor from '../networkyaml';



// Node header component with improved styling
interface NodeHeaderProps {
  type: string;
  ip: string;
  label: string;
}

const NodeHeader: React.FC<NodeHeaderProps> = ({ type, ip, label }) => {
  // Select icon based on node type
  const getIcon = () => {
    switch (type.toLowerCase()) {
      case 'pod':
        return <Rocket className="h-4 w-4" />;
      case 'service':
        return <Server className="h-4 w-4" />;
      case 'external':
        return <Globe className="h-4 w-4" />;
      default:
        return <Database className="h-4 w-4" />;
    }
  };

  // Select background color based on node type
  const getBackgroundColor = () => {
    switch (type.toLowerCase()) {
      case 'pod':
        return 'bg-orange-100 dark:bg-orange-900';
      case 'external':
        return type === 'external' ? 'bg-red-100 dark:bg-red-900' : 'bg-blue-100 dark:bg-blue-900';
      default:
        return 'bg-blue-100 dark:bg-blue-900';
    }
  };

  return (
    <div className={`p-2 rounded-md border border-border ${getBackgroundColor()}`}>
      <div className="flex items-center justify-center mb-1">
        {getIcon()}
      </div>
      <div className="text-xs space-y-0.5">
        <div className="font-semibold">{type}</div>
        <div className="truncate max-w-[120px]" title={ip}>{ip}</div>
        <div className="truncate max-w-[120px]" title={label}>{label}</div>
      </div>
    </div>
  );
};

interface AnimatedSvgEdgeProps {
  id: string;
  sourceX: number;
  sourceY: number;
  targetX: number;
  targetY: number;
  sourcePosition: Position;
  targetPosition: Position;
  data: {
    label: string;
  };
}

const AnimatedSvgEdge: React.FC<AnimatedSvgEdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  data
}) => {
  const [edgePath] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition
  });

  return (
    <>
      <BaseEdge id={id} path={edgePath} />
      <circle r="4" fill="var(--primary)">
        <animateMotion dur="3s" repeatCount="indefinite" path={edgePath} />
      </circle>

      <text
        x={(sourceX + targetX) / 2}
        y={(sourceY + targetY) / 2 - 5}
        style={{
          fontSize: '10px',
          fill: 'var(--foreground)',
          textAnchor: 'middle',
          pointerEvents: 'none',
          fontWeight: 'bold',
          textShadow: '0 0 2px var(--background)'
        }}
      >
        {data.label}
      </text>
    </>
  );
};

// Type definitions
interface NamespaceData {
  id: string;
  name: string;
  status: string;
  quota: string;
  createdAt: string;
  labels: string;
  annotation: string;
}

interface NetworkData {
  centerIp: string;
  srcIP: string;
  srcType: string;
  srcLabel: string;
  dstIP: string;
  dstType: string;
  dstLabel: string;
  dstPort: number;
}

interface PodLabelData {
  podName: string;
  labels: { [key: string]: string };
}

interface PodLabelResponse {
  namespace: string;
  pods: PodLabelData[];
}

interface LabelData {
  key: string;
  value: string;
}


// Custom edge types
const edgeTypes = {
  'animated-svg': AnimatedSvgEdge,
};

const NetworkDiagram: React.FC = () => {
  const [reactFlowNodes, setReactFlowNodes, onNodesChange] = useNodesState<Node>([]);
  const [reactFlowEdges, setReactFlowEdges, onEdgesChange] = useEdgesState<Edge>([]);
  const [selectedNamespace, setSelectedNamespace] = useState("");
  const [selectedLabel, setSelectedLabel] = useState("");
  const [namespaces, setNamespaces] = useState<NamespaceData[]>([]);
  const [labels, setLabels] = useState<LabelData[]>([]);
  const [networkData, setNetworkData] = useState<NetworkData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Add state for YAML editor
  const [yamlContent, setYamlContent] = useState<string>("");
  const [showYamlEditor, setShowYamlEditor] = useState<boolean>(false);

  // Fetch namespaces
  const fetchNamespaces = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios_api.get('/k8s/namespaces');
      setNamespaces(response.data);
    } catch (err: any) {
      setError(`Failed to fetch namespaces: ${err.message}`);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch pod labels for a namespace
  const fetchLabels = useCallback(async (namespace: string) => {
    if (!namespace) return;
    
    setLoading(true);
    setError(null);
    try {
      const response = await axios_api.get<PodLabelResponse>('/k8s/pod-labels', {
        params: { namespace }
      });

      // Extract unique app labels
      const appLabels = new Map<string, string>();
      
      response.data.pods.forEach(pod => {
        if (pod.labels && pod.labels.app) {
          appLabels.set(pod.labels.app, pod.labels.app);
        }
      });
      
      const labelData: LabelData[] = Array.from(appLabels.entries()).map(([key, value]) => ({
        key,
        value
      }));
      
      setLabels(labelData);
    } catch (err: any) {
      setError(`Failed to fetch labels: ${err.message}`);
      setLabels([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Initial load of namespaces
  useEffect(() => {
    fetchNamespaces();
  }, [fetchNamespaces]);

  // Load labels when namespace changes
  useEffect(() => {
    if (selectedNamespace) {
      fetchLabels(selectedNamespace);
    } else {
      setLabels([]);
    }
  }, [selectedNamespace, fetchLabels]);

  // Fetch network data
  const fetchData = async () => {
    if (!selectedNamespace || !selectedLabel) {
      setError("Please select both a namespace and a label");
      return;
    }

    setLoading(true);
    setError(null);
    try {
      const response = await axios_api.post<NetworkData[]>('/k8s/search-pods-v2-network-flow', {
        namespace: selectedNamespace,
        labelKey: "app",
        labelValue: selectedLabel,
      });

      setNetworkData(response.data);
      
      if (response.data.length === 0) {
        setError("No network data found for the selected criteria");
      } else {
        // Generate and set nodes and edges
        const { nodes, edges } = generateElements(response.data);
        setReactFlowNodes(nodes);
        setReactFlowEdges(edges);
      }
    } catch (err: any) {
      setError(`Failed to fetch network data: ${err.message}`);
      setNetworkData([]);
      setReactFlowNodes([]);
      setReactFlowEdges([]);
    } finally {
      setLoading(false);
    }
  };

  // Generate nodes and edges from network data
  const generateElements = (data: NetworkData[]) => {
    const nodes: { [key: string]: Node } = {};
    const edges: Edge[] = [];

    if (data.length === 0) {
      return { nodes: [], edges: [] };
    }

    // Extract all center IPs
    const centerIps = Array.from(new Set(data.flatMap(item => 
      item.centerIp.split(',').map(ip => ip.trim())
    )));
    
    // Layout configuration
    const spacingX = 250;
    const spacingY = 150;
    const gridWidth = 6;

    let inputIndex = 0;
    let centerIndex = 0;
    let outputIndex = 0;

    // Create center nodes
    centerIps.forEach(centerIp => {
      const x = (centerIndex % gridWidth) * spacingX + 100;
      const y = 300 + Math.floor(centerIndex / gridWidth) * spacingY;

      nodes[centerIp] = {
        id: centerIp,
        data: { label: <NodeHeader type="pod" ip={centerIp} label={selectedLabel} /> },
        position: { x, y },
        style: { width: 150 }
      };
      centerIndex++;
    });

    // Process network data to create nodes and edges
    data.forEach(({ centerIp, srcIP, srcType, srcLabel, dstIP, dstType, dstLabel, dstPort }) => {
      const centerIpsArray = centerIp.split(',').map(ip => ip.trim());

      // Create source node if it doesn't exist
      if (!nodes[srcIP]) {
        const x = (inputIndex % gridWidth) * spacingX + 100;
        const y = 100 + Math.floor(inputIndex / gridWidth) * spacingY;

        nodes[srcIP] = {
          id: srcIP,
          data: { label: <NodeHeader type={srcType} ip={srcIP} label={srcLabel} /> },
          position: { x, y },
          style: { width: 150 }
        };
        inputIndex++;
      }

      // Create destination node if it doesn't exist
      if (!nodes[dstIP]) {
        const x = (outputIndex % gridWidth) * spacingX + 100;
        const y = 500 + Math.floor(outputIndex / gridWidth) * spacingY;

        nodes[dstIP] = {
          id: dstIP,
          data: { label: <NodeHeader type={dstType} ip={dstIP} label={dstLabel} /> },
          position: { x, y },
          style: { width: 150 }
        };
        outputIndex++;
      }

      // Create edges between nodes
      centerIpsArray.forEach(centerIp => {
        // Edge from source to center
        const edgeSourceToCenterId = `e-${srcIP}-${centerIp}-${dstPort}`;
        if (!edges.find(edge => edge.id === edgeSourceToCenterId)) {
          edges.push({
            id: edgeSourceToCenterId,
            source: srcIP,
            target: centerIp,
            type: 'animated-svg',
            data: { label: String(dstPort) }
          });
        }

        // Edge from center to destination
        const edgeCenterToDestinationId = `e-${centerIp}-${dstIP}-${dstPort}`;
        if (!edges.find(edge => edge.id === edgeCenterToDestinationId)) {
          edges.push({
            id: edgeCenterToDestinationId,
            source: centerIp,
            target: dstIP,
            type: 'animated-svg',
            data: { label: String(dstPort) }
          });
        }
      });
    });

    // Add section labels
    nodes["label-input"] = {
      id: "label-input",
      data: { 
        label: <Badge variant="outline" className="text-sm font-bold">🔵 Input Nodes</Badge>
      },
      position: { x: 20, y: 50 },
      draggable: false,
      selectable: false,
      style: { backgroundColor: 'transparent', border: 'none' }
    };

    nodes["label-center"] = {
      id: "label-center",
      data: { 
        label: <Badge variant="outline" className="text-sm font-bold">🟠 Center Nodes</Badge>
      },
      position: { x: 20, y: 250 },
      draggable: false,
      selectable: false,
      style: { backgroundColor: 'transparent', border: 'none' }
    };

    nodes["label-output"] = {
      id: "label-output",
      data: { 
        label: <Badge variant="outline" className="text-sm font-bold">🟢 Output Nodes</Badge>
      },
      position: { x: 20, y: 450 },
      draggable: false,
      selectable: false,
      style: { backgroundColor: 'transparent', border: 'none' }
    };

    // Filter out edges between center nodes
    const filteredEdges = edges.filter(edge => 
      !(centerIps.includes(edge.source as string) && centerIps.includes(edge.target as string))
    );

    return { 
      nodes: Object.values(nodes), 
      edges: filteredEdges 
    };
  };

  // Generate YAML for network policy
  const handleShowYaml = async () => {
    if (!selectedNamespace || !selectedLabel) {
      setError("Please select both a namespace and a label before generating YAML");
      return;
    }

    setLoading(true);
    setError(null);
    try {
      const response = await axios_api.post("/k8s/generate-policy", {
        namespace: selectedNamespace,
        labelKey: "app",
        labelValue: selectedLabel,
      });

      // Set YAML content and open editor
      setYamlContent(response.data);
      setShowYamlEditor(true);
    } catch (err: any) {
      setError(`Failed to generate YAML: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full h-full">
      <CardContent className="p-4">
        <div className="flex flex-wrap gap-2 mb-4 items-center">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="w-[180px]">
                  <Select 
                    value={selectedNamespace} 
                    onValueChange={setSelectedNamespace}
                    disabled={loading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select Namespace" />
                    </SelectTrigger>
                    <SelectContent>
                      {namespaces.map((ns) => (
                        <SelectItem key={ns.name} value={ns.name}>
                          {ns.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </TooltipTrigger>
              <TooltipContent>Select a Kubernetes namespace</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="w-[180px]">
                  <Select 
                    value={selectedLabel} 
                    onValueChange={setSelectedLabel}
                    disabled={loading || labels.length === 0}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select App Label" />
                    </SelectTrigger>
                    <SelectContent>
                      {labels.map((label) => (
                        <SelectItem key={label.key} value={label.value}>
                          {label.value}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </TooltipTrigger>
              <TooltipContent>Select an application label</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Button 
            onClick={fetchData} 
            disabled={loading || !selectedNamespace || !selectedLabel}
            className="ml-auto"
          >
            {loading ? "Loading..." : "Fetch Network Data"}
          </Button>
          
          <Button 
            onClick={handleShowYaml} 
            variant="outline"
            disabled={loading || networkData.length === 0}
          >
            Generate Network Policy
          </Button>
        </div>

        {error && (
          <div className="bg-destructive/15 text-destructive p-2 rounded-md mb-4 text-sm">
            {error}
          </div>
        )}

        <div className="w-full h-[600px] border rounded-md">
          <ReactFlow
            nodes={reactFlowNodes}
            edges={reactFlowEdges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            edgeTypes={edgeTypes}
            fitView
            minZoom={0.1}
            maxZoom={2}
            defaultViewport={{ x: 0, y: 0, zoom: 0.8 }}
          >
            <Background />
            <Controls />
            <Panel position="top-right" className="bg-card p-2 rounded-md shadow-md text-xs">
              <div className="font-medium">Network Flow Diagram</div>
              <div className="text-muted-foreground">
                {selectedNamespace && selectedLabel ? 
                  `Namespace: ${selectedNamespace}, App: ${selectedLabel}` : 
                  "Select namespace and label to view network flow"}
              </div> 
            </Panel>
          </ReactFlow>
        </div>
        
        {/* YAML Editor Dialog */}
        <NetworkYamlEditor
          open={showYamlEditor}
          yamlContent={yamlContent}
          onClose={() => setShowYamlEditor(false)}
        />
      </CardContent>
    </Card>
  );
};

export default NetworkDiagram;
